import { post } from '@/shared/utils/request';
import type { 
  VenueAuthStatusVO, 
  AuditVenueReq, 
  GenerateAuthReq, 
  ActivateAuthReq,
  GetAuthStatusReq
} from '@/entities/store/license';


export function getAuthStatus(data: GetAuthStatusReq) {
  return post<VenueAuthStatusVO>('/api/business/venue/auth/status', data);
}

export function auditVenue(data: AuditVenueReq) {
  return post<any>('/api/business/venue/auth/audit', data);
}

export function generateAuth(data: GenerateAuthReq) {
  return post<string>('/api/business/venue/auth/generate', data);
}

export function activateAuth(data: ActivateAuthReq) {
  return post<any>('/api/business/venue/auth/activate', data);
}

export function queryVenueAuthCodes(params?: {
  venueId?: string;
  status?: number;
  pageNum?: number;
  pageSize?: number;
}) {
  return post<any>('/api/business/venue/auth/auth-codes', params || {});
}