import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/shared/store/authStore'

// 扩展路由元信息类型
declare module 'vue-router' {
  interface RouteMeta {
    title: string
    requiresAuth?: boolean
  }
}

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../modules/auth/login/index.vue'),
    meta: { 
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/',
    redirect: '/dashboard',
    meta: { 
      title: '主页',
      requiresAuth: true
    },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('../views/dashboard/index.vue'),
        meta: { title: '控制台' }
      },
      {
        path: 'store',
        name: 'Store',
        children: [
          {
            path: 'audit',
            name: 'StoreAudit',
            component: () => import('../modules/store/audit/list/index.vue'),
            meta: { title: '门店审核' }
          },
          // {
          //   path: 'audit/detail/:id',
          //   name: 'StoreAuditDetail',
          //   component: () => import('../modules/store/audit/detail/index.vue'),
          //   meta: { title: '审核详情' }
          // },
          {
            path: 'license',
            name: 'StoreLicense',
            component: () => import('../modules/store/license/generate/index.vue'),
            meta: { title: '授权管理' }
          },
          {
            path: 'data-import',
            name: 'DataImport',
            children: [
              {
                path: '',
                name: 'DataImportOverview',
                component: () => import('../modules/store/data-import/views/index.vue'),
                meta: { title: '数据导入' }
              },
              {
                path: ':pluginId',
                name: 'DataImportWizard',
                component: () => import('../modules/store/data-import/views/import-wizard.vue'),
                meta: { title: '数据导入向导' }
              }
            ]
          },
          // {
          //   path: 'license/history',
          //   name: 'StoreLicenseHistory',
          //   component: () => import('../modules/store/license/history/index.vue'),
          //   meta: { title: '授权历史' }
          // }
        ]
      },
      {
        path: 'exception',
        name: 'Exception',
        component: () => import('../views/exception/index.vue'),
        meta: { 
          title: '异常处理',
          requiresAuth: true
        }
      },
      {
        path: 'appversion',
        name: 'AppVersion',
        children: [
          {
            path: 'manage',
            name: 'AppVersionManage',
            component: () => import('../modules/appversion/manage/index.vue'),
            meta: { title: '应用版本管理' }
          }
        ]
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('../modules/auth/profile/index.vue'),
        meta: { 
          title: '个人中心',
          requiresAuth: true
        }
      }
    ]
  }
]

export const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)

  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 商务授权后台管理系统` : '商务授权后台管理系统'

  if (requiresAuth && !authStore.isAuthenticated) {
    // 需要认证但未登录，重定向到登录页
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
  } else if (to.path === '/login' && authStore.isAuthenticated) {
    // 已登录但访问登录页，重定向到首页
    next({ path: '/profile' })
  } else {
    next()
  }
}) 