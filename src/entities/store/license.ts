/**
 * 门店授权状态视图对象，对应服务端 VO
 */
export interface VenueAuthStatusVO {
    venue_id: string;         // 门店ID
    auth_status: number;      // 授权状态：0-未授权 1-试用中 2-正式授权 3-已过期
    expire_time: string;      // 到期时间
    remain_days: number;      // 剩余天数
    auth_type: number;        // 授权类型：1-试用授权 2-正式授权
    last_auth_time: string;   // 最近授权时间
    last_audit_time: string;  // 最近审核时间
    audit_remark: string;     // 审核备注
    contract_no: string;      // 合同编号
    contract_name: string;    // 合同名称
}

/**
 * 审核门店请求参数
 */
export interface AuditVenueReq {
    venue_id: string; // 门店ID
    status: number;   // 操作状态：例如 0-待审核 1-通过 2-拒绝
    remark?: string;  // 可选的审核备注
}

/**
 * 生成授权码请求参数
 */
export interface GenerateAuthReq {
    venue_ids: string[];  // 门店ID数组
    status: number;       // 操作状态（数字枚举）
    templateId?: string;  // 可选的审核模板ID
    remark: string;       // 备注信息
}

/**
 * 激活授权请求参数
 */
export interface ActivateAuthReq {
    venue_id: string;   // 门店ID
    auth_code: string;  // 生成的授权码
}

/**
 * 获取授权状态请求参数（对应服务端GetAuthStatusReqDto）
 */
export interface GetAuthStatusReq {
  venue_id: string; // 门店ID
}
