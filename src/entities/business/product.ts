/**
 * 商务后台产品管理相关实体类型定义
 */

/**
 * 产品查询请求DTO
 */
export interface ProductQueryReqDto {
  /** 产品编码 */
  productCode?: string;
  /** 产品名称 */
  productName?: string;
  /** 分类ID */
  categoryId?: string;
  /** 品牌ID */
  brandId?: string;
  /** 产品状态 */
  status?: 'ACTIVE' | 'INACTIVE' | 'DISCONTINUED';
  /** 价格范围 - 最小价格 */
  minPrice?: number;
  /** 价格范围 - 最大价格 */
  maxPrice?: number;
  /** 创建时间范围 - 开始时间 */
  createTimeStart?: string;
  /** 创建时间范围 - 结束时间 */
  createTimeEnd?: string;
  /** 页码 */
  pageNum?: number;
  /** 每页大小 */
  pageSize?: number;
  /** 排序字段 */
  sortBy?: string;
  /** 排序方向 */
  sortOrder?: 'ASC' | 'DESC';
}

/**
 * 产品查询响应VO
 */
export interface ProductQueryVO {
  /** 产品列表 */
  list?: ProductItem[];
  /** 总记录数 */
  total?: number;
  /** 当前页码 */
  pageNum?: number;
  /** 每页大小 */
  pageSize?: number;
  /** 总页数 */
  pages?: number;
}

/**
 * 产品项
 */
export interface ProductItem {
  /** 产品ID */
  productId?: string;
  /** 产品编码 */
  productCode?: string;
  /** 产品名称 */
  productName?: string;
  /** 产品描述 */
  description?: string;
  /** 分类ID */
  categoryId?: string;
  /** 分类名称 */
  categoryName?: string;
  /** 品牌ID */
  brandId?: string;
  /** 品牌名称 */
  brandName?: string;
  /** 规格 */
  specification?: string;
  /** 单位 */
  unit?: string;
  /** 成本价 */
  costPrice?: number;
  /** 建议零售价 */
  retailPrice?: number;
  /** 批发价 */
  wholesalePrice?: number;
  /** 产品状态 */
  status?: 'ACTIVE' | 'INACTIVE' | 'DISCONTINUED';
  /** 产品图片URL列表 */
  imageUrls?: string[];
  /** 条形码 */
  barcode?: string;
  /** 重量（克） */
  weight?: number;
  /** 体积（立方厘米） */
  volume?: number;
  /** 保质期（天） */
  shelfLife?: number;
  /** 存储条件 */
  storageConditions?: string;
  /** 供应商ID */
  supplierId?: string;
  /** 供应商名称 */
  supplierName?: string;
  /** 创建人 */
  createBy?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新人 */
  updateBy?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
} 