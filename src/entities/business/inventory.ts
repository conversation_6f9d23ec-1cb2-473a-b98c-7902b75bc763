/**
 * 商务后台库存管理相关实体类型定义
 */

/**
 * 库存导入预览请求DTO
 */
export interface StockImportPreviewReqDto {
  /** 文件内容或文件路径 */
  fileContent?: string;
  /** 文件名 */
  fileName?: string;
  /** 门店ID */
  venueId?: string;
  /** 导入类型 */
  importType?: string;
}

/**
 * 库存导入预览响应VO
 */
export interface StockImportPreviewVO {
  /** 预览数据列表 */
  previewData?: StockPreviewItem[];
  /** 总记录数 */
  totalCount?: number;
  /** 有效记录数 */
  validCount?: number;
  /** 无效记录数 */
  invalidCount?: number;
  /** 错误信息列表 */
  errors?: ImportError[];
}

/**
 * 库存预览项
 */
export interface StockPreviewItem {
  /** 行号 */
  rowNumber?: number;
  /** 产品编码 */
  productCode?: string;
  /** 产品名称 */
  productName?: string;
  /** 库存数量 */
  stockQuantity?: number;
  /** 单位 */
  unit?: string;
  /** 价格 */
  price?: number;
  /** 是否有效 */
  isValid?: boolean;
  /** 错误信息 */
  errorMessage?: string;
}

/**
 * 导入错误信息
 */
export interface ImportError {
  /** 行号 */
  rowNumber?: number;
  /** 错误字段 */
  field?: string;
  /** 错误消息 */
  message?: string;
  /** 错误代码 */
  code?: string;
}

/**
 * 库存导入执行请求DTO
 */
export interface StockImportExecuteReqDto {
  /** 文件内容或文件路径 */
  fileContent?: string;
  /** 文件名 */
  fileName?: string;
  /** 门店ID */
  venueId?: string;
  /** 导入类型 */
  importType?: string;
  /** 是否跳过错误行 */
  skipErrors?: boolean;
}

/**
 * 库存导入执行响应VO
 */
export interface StockImportExecuteVO {
  /** 导入任务ID */
  taskId?: string;
  /** 导入状态 */
  status?: 'SUCCESS' | 'FAILED' | 'PARTIAL';
  /** 成功导入数量 */
  successCount?: number;
  /** 失败数量 */
  failureCount?: number;
  /** 总数量 */
  totalCount?: number;
  /** 错误信息列表 */
  errors?: ImportError[];
  /** 导入结果消息 */
  message?: string;
}

/**
 * 门店库存列表查询请求DTO
 */
export interface VenueStockListReqDto {
  /** 门店ID */
  venueId?: string;
  /** 产品编码 */
  productCode?: string;
  /** 产品名称 */
  productName?: string;
  /** 分类ID */
  categoryId?: string;
  /** 库存状态 */
  stockStatus?: 'IN_STOCK' | 'OUT_OF_STOCK' | 'LOW_STOCK';
  /** 页码 */
  pageNum?: number;
  /** 每页大小 */
  pageSize?: number;
  /** 排序字段 */
  sortBy?: string;
  /** 排序方向 */
  sortOrder?: 'ASC' | 'DESC';
}

/**
 * 门店库存列表响应VO
 */
export interface VenueStockListVO {
  /** 库存列表 */
  list?: VenueStockItem[];
  /** 总记录数 */
  total?: number;
  /** 当前页码 */
  pageNum?: number;
  /** 每页大小 */
  pageSize?: number;
  /** 总页数 */
  pages?: number;
}

/**
 * 门店库存项
 */
export interface VenueStockItem {
  /** 库存ID */
  stockId?: string;
  /** 门店ID */
  venueId?: string;
  /** 门店名称 */
  venueName?: string;
  /** 产品ID */
  productId?: string;
  /** 产品编码 */
  productCode?: string;
  /** 产品名称 */
  productName?: string;
  /** 产品分类 */
  categoryName?: string;
  /** 当前库存数量 */
  currentStock?: number;
  /** 可用库存数量 */
  availableStock?: number;
  /** 预留库存数量 */
  reservedStock?: number;
  /** 单位 */
  unit?: string;
  /** 成本价 */
  costPrice?: number;
  /** 售价 */
  salePrice?: number;
  /** 库存状态 */
  stockStatus?: 'IN_STOCK' | 'OUT_OF_STOCK' | 'LOW_STOCK';
  /** 最低库存预警值 */
  minStockAlert?: number;
  /** 最后更新时间 */
  lastUpdateTime?: string;
  /** 创建时间 */
  createTime?: string;
} 