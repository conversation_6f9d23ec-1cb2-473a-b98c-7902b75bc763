// 库存数据导入 - 模板生成器

import type { TemplateGenerator, TemplateData } from '../../core/types'

/**
 * 库存模板生成器
 */
export class InventoryTemplateGenerator implements TemplateGenerator {
  getTemplateData(): TemplateData {
    return {
      headers: ['商品编号', '库存数量', '安全库存'],
      fields: [
        {
          key: '商品编号',
          label: '商品编号',
          type: 'string',
          required: true,
          maxLength: 50,
          description: '商品的唯一标识',
          example: 'P001'
        },
        {
          key: '库存数量',
          label: '库存数量',
          type: 'number',
          required: true,
          description: '当前实际库存数量',
          example: '100'
        },
        {
          key: '安全库存',
          label: '安全库存',
          type: 'number',
          required: false,
          description: '库存预警阈值，低于此值将产生预警',
          example: '20'
        }
      ],
      validationRules: [
        {
          field: '商品编号',
          type: 'required',
          message: '商品编号不能为空'
        },
        {
          field: '库存数量',
          type: 'required',
          message: '库存数量不能为空'
        },
        {
          field: '库存数量',
          type: 'type',
          message: '库存数量必须是数字'
        },
        {
          field: '安全库存',
          type: 'type',
          message: '安全库存必须是数字'
        }
      ],
      exampleData: [
        {
          '商品编号': 'P001',
          '库存数量': '100',
          '安全库存': '20'
        },
        {
          '商品编号': 'P002',
          '库存数量': '50',
          '安全库存': '10'
        },
        {
          '商品编号': 'P003',
          '库存数量': '200',
          '安全库存': '30'
        }
      ],
      instructions: [
        '请按照以下要求填写库存信息：',
        '• 商品编号：对应商品的唯一标识，不能重复',
        '• 库存数量：当前实际库存数量，必须为非负整数',
        '• 安全库存：库存预警阈值（可选），低于此值将产生预警',
        '',
        '注意事项：',
        '• 商品编号不能为空且不能重复',
        '• 数字字段请填写有效数值',
        '• 保存为CSV格式文件，编码为UTF-8',
        '• 建议先下载模板文件，在模板基础上填写数据'
      ],
      description: '库存信息导入模板，支持商品库存数量和安全库存设置'
    }
  }

  generateCSV(): string {
    const templateData = this.getTemplateData()
    const headers = templateData.headers.join(',')
    const examples = templateData.exampleData.map(row => 
      templateData.headers.map(header => row[header] || '').join(',')
    ).join('\n')
    
    return `${headers}\n${examples}`
  }

  getExampleData(): Record<string, any>[] {
    return this.getTemplateData().exampleData
  }
} 