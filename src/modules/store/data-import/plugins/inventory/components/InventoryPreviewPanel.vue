<template>
  <div class="inventory-preview-panel">
    <!-- 预览摘要 -->
    <div class="preview-summary mb-6">
      <h3 class="text-xl font-semibold mb-4">库存数据预览</h3>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div class="text-sm text-blue-600 font-medium">待导入记录</div>
          <div class="text-2xl font-bold text-blue-700">{{ totalRecords }}</div>
        </div>
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
          <div class="text-sm text-green-600 font-medium">商品种类</div>
          <div class="text-2xl font-bold text-green-700">{{ uniqueProductCount }}</div>
        </div>
        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <div class="text-sm text-purple-600 font-medium">总库存量</div>
          <div class="text-2xl font-bold text-purple-700">{{ totalStockQuantity.toLocaleString() }}</div>
        </div>
        <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <div class="text-sm text-orange-600 font-medium">库存价值估算</div>
          <div class="text-2xl font-bold text-orange-700">¥{{ estimatedValue.toLocaleString() }}</div>
        </div>
      </div>
    </div>

    <!-- 库存分析图表 -->
    <div class="inventory-analysis mb-6">
      <h4 class="text-lg font-semibold mb-4">库存分析</h4>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        <!-- 库存分布 -->
        <div class="bg-white border rounded-lg p-4">
          <h5 class="font-medium mb-3">库存量分布</h5>
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">高库存 (>1000)</span>
              <div class="flex items-center gap-2">
                <div class="w-16 bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-green-500 h-2 rounded-full" 
                    :style="{ width: `${highStockPercentage}%` }"
                  ></div>
                </div>
                <span class="text-sm font-medium">{{ highStockCount }}</span>
              </div>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">中库存 (100-1000)</span>
              <div class="flex items-center gap-2">
                <div class="w-16 bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-blue-500 h-2 rounded-full" 
                    :style="{ width: `${mediumStockPercentage}%` }"
                  ></div>
                </div>
                <span class="text-sm font-medium">{{ mediumStockCount }}</span>
              </div>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">低库存 (<100)</span>
              <div class="flex items-center gap-2">
                <div class="w-16 bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-yellow-500 h-2 rounded-full" 
                    :style="{ width: `${lowStockPercentage}%` }"
                  ></div>
                </div>
                <span class="text-sm font-medium">{{ lowStockCount }}</span>
              </div>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">零库存</span>
              <div class="flex items-center gap-2">
                <div class="w-16 bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-red-500 h-2 rounded-full" 
                    :style="{ width: `${zeroStockPercentage}%` }"
                  ></div>
                </div>
                <span class="text-sm font-medium">{{ zeroStockCount }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 预警信息 -->
        <div class="bg-white border rounded-lg p-4">
          <h5 class="font-medium mb-3">库存预警</h5>
          <div class="space-y-3">
            <div class="flex items-center gap-3 p-3 bg-red-50 border border-red-200 rounded-lg">
              <el-icon class="text-red-500"><WarningFilled /></el-icon>
              <div>
                <div class="text-sm font-medium text-red-700">安全库存不足</div>
                <div class="text-xs text-red-600">{{ belowSafeStockCount }} 个商品低于安全库存</div>
              </div>
            </div>
            <div class="flex items-center gap-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <el-icon class="text-yellow-500"><Warning /></el-icon>
              <div>
                <div class="text-sm font-medium text-yellow-700">接近安全库存</div>
                <div class="text-xs text-yellow-600">{{ nearSafeStockCount }} 个商品接近安全库存</div>
              </div>
            </div>
            <div class="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
              <el-icon class="text-green-500"><CircleCheckFilled /></el-icon>
              <div>
                <div class="text-sm font-medium text-green-700">库存充足</div>
                <div class="text-xs text-green-600">{{ safeStockCount }} 个商品库存充足</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 库存数据表格 -->
    <div class="inventory-table mb-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-lg font-semibold">库存明细</h4>
        <div class="flex items-center gap-3">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索商品编号..."
            size="small"
            style="width: 200px;"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select v-model="filterStatus" placeholder="筛选状态" size="small" style="width: 120px;">
            <el-option label="全部" value="all" />
            <el-option label="正常" value="normal" />
            <el-option label="低库存" value="low" />
            <el-option label="零库存" value="zero" />
          </el-select>
        </div>
      </div>

      <el-table
        :data="filteredData"
        style="width: 100%"
        max-height="500"
        stripe
        border
        :row-class-name="getRowClassName"
      >
        <el-table-column prop="商品编号" label="商品编号" width="130" fixed="left">
          <template #default="{ row }">
            <span class="font-mono">{{ row['商品编号'] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="库存数量" label="当前库存" width="100" align="right" sortable>
          <template #default="{ row }">
            <span :class="getStockQuantityClass(row['库存数量'])">
              {{ formatNumber(row['库存数量']) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="安全库存" label="安全库存" width="100" align="right" sortable>
          <template #default="{ row }">
            <span class="text-gray-600">{{ formatNumber(row['安全库存']) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="库存状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStockStatusTagType(row)" size="small">
              {{ getStockStatusText(row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="差值" width="80" align="right">
          <template #default="{ row }">
            <span :class="getDifferenceClass(row)">
              {{ getStockDifference(row) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="库存天数" width="100" align="right">
          <template #default="{ row }">
            <span class="text-sm text-gray-600">
              {{ getStockDays(row) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作建议" min-width="120">
          <template #default="{ row }">
            <span class="text-sm" :class="getSuggestionClass(row)">
              {{ getSuggestion(row) }}
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="mt-4 flex justify-center">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="filteredData.length"
        />
      </div>
    </div>

    <!-- 底部操作 -->
    <div class="flex justify-between items-center">
      <div class="text-sm text-gray-500">
        确认数据无误后，点击"下一步"开始导入
      </div>
      <div class="space-x-3">
        <el-button @click="$emit('prev')">
          上一步
        </el-button>
        <el-button type="primary" @click="$emit('next')">
          确认导入
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { WarningFilled, Warning, CircleCheckFilled, Search } from '@element-plus/icons-vue'
import type { ImportStepProps, ImportStepEmits } from '../../../core/types'

// Props 和 Emits
const props = defineProps<ImportStepProps>()
const emit = defineEmits<ImportStepEmits>()

// 响应式数据
const searchKeyword = ref('')
const filterStatus = ref('all')
const currentPage = ref(1)
const pageSize = ref(20)

// 计算属性
const previewData = computed(() => props.stepData.parsedData?.data || [])

const totalRecords = computed(() => previewData.value.length)

const uniqueProductCount = computed(() => {
  const products = new Set(previewData.value.map((row: any) => row['商品编号']))
  return products.size
})

const totalStockQuantity = computed(() => {
  return previewData.value.reduce((total: number, row: any) => {
    return total + (parseInt(row['库存数量']) || 0)
  }, 0)
})

const estimatedValue = computed(() => {
  // 简单估算：假设平均单价50元
  return totalStockQuantity.value * 50
})

// 库存分布统计
const stockDistribution = computed(() => {
  const high = previewData.value.filter((row: any) => (parseInt(row['库存数量']) || 0) > 1000).length
  const medium = previewData.value.filter((row: any) => {
    const qty = parseInt(row['库存数量']) || 0
    return qty >= 100 && qty <= 1000
  }).length
  const low = previewData.value.filter((row: any) => {
    const qty = parseInt(row['库存数量']) || 0
    return qty > 0 && qty < 100
  }).length
  const zero = previewData.value.filter((row: any) => (parseInt(row['库存数量']) || 0) === 0).length
  
  return { high, medium, low, zero }
})

const highStockCount = computed(() => stockDistribution.value.high)
const mediumStockCount = computed(() => stockDistribution.value.medium)
const lowStockCount = computed(() => stockDistribution.value.low)
const zeroStockCount = computed(() => stockDistribution.value.zero)

const highStockPercentage = computed(() => 
  totalRecords.value > 0 ? (highStockCount.value / totalRecords.value) * 100 : 0
)
const mediumStockPercentage = computed(() => 
  totalRecords.value > 0 ? (mediumStockCount.value / totalRecords.value) * 100 : 0
)
const lowStockPercentage = computed(() => 
  totalRecords.value > 0 ? (lowStockCount.value / totalRecords.value) * 100 : 0
)
const zeroStockPercentage = computed(() => 
  totalRecords.value > 0 ? (zeroStockCount.value / totalRecords.value) * 100 : 0
)

// 安全库存预警
const safeStockAnalysis = computed(() => {
  const belowSafe = previewData.value.filter((row: any) => {
    const current = parseInt(row['库存数量']) || 0
    const safe = parseInt(row['安全库存']) || 0
    return safe > 0 && current < safe
  }).length

  const nearSafe = previewData.value.filter((row: any) => {
    const current = parseInt(row['库存数量']) || 0
    const safe = parseInt(row['安全库存']) || 0
    return safe > 0 && current >= safe && current <= safe * 1.2
  }).length

  const safe = previewData.value.filter((row: any) => {
    const current = parseInt(row['库存数量']) || 0
    const safe_stock = parseInt(row['安全库存']) || 0
    return safe_stock > 0 && current > safe_stock * 1.2
  }).length

  return { belowSafe, nearSafe, safe }
})

const belowSafeStockCount = computed(() => safeStockAnalysis.value.belowSafe)
const nearSafeStockCount = computed(() => safeStockAnalysis.value.nearSafe)
const safeStockCount = computed(() => safeStockAnalysis.value.safe)

// 过滤后的数据
const filteredData = computed(() => {
  let filtered = previewData.value

  // 搜索过滤
  if (searchKeyword.value) {
    filtered = filtered.filter((row: any) => 
      row['商品编号']?.toString().toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  // 状态过滤
  if (filterStatus.value !== 'all') {
    filtered = filtered.filter((row: any) => {
      const qty = parseInt(row['库存数量']) || 0
      const safe = parseInt(row['安全库存']) || 0
      
      switch (filterStatus.value) {
        case 'normal':
          return safe === 0 || qty > safe
        case 'low':
          return safe > 0 && qty > 0 && qty <= safe
        case 'zero':
          return qty === 0
        default:
          return true
      }
    })
  }

  return filtered
})

// 方法
const formatNumber = (value: any): string => {
  const num = parseInt(value) || 0
  return num.toLocaleString()
}

const getStockQuantityClass = (quantity: any): string => {
  const qty = parseInt(quantity) || 0
  if (qty === 0) return 'text-red-600 font-bold'
  if (qty < 100) return 'text-yellow-600 font-semibold'
  return 'text-gray-900'
}

const getStockStatusTagType = (row: any): string => {
  const current = parseInt(row['库存数量']) || 0
  const safe = parseInt(row['安全库存']) || 0
  
  if (current === 0) return 'danger'
  if (safe > 0 && current <= safe) return 'warning'
  return 'success'
}

const getStockStatusText = (row: any): string => {
  const current = parseInt(row['库存数量']) || 0
  const safe = parseInt(row['安全库存']) || 0
  
  if (current === 0) return '零库存'
  if (safe > 0 && current <= safe) return '低库存'
  return '正常'
}

const getStockDifference = (row: any): string => {
  const current = parseInt(row['库存数量']) || 0
  const safe = parseInt(row['安全库存']) || 0
  
  if (safe === 0) return '-'
  const diff = current - safe
  return diff >= 0 ? `+${diff}` : `${diff}`
}

const getDifferenceClass = (row: any): string => {
  const current = parseInt(row['库存数量']) || 0
  const safe = parseInt(row['安全库存']) || 0
  
  if (safe === 0) return 'text-gray-400'
  const diff = current - safe
  if (diff < 0) return 'text-red-600'
  if (diff === 0) return 'text-yellow-600'
  return 'text-green-600'
}

const getStockDays = (row: any): string => {
  const current = parseInt(row['库存数量']) || 0
  if (current === 0) return '0天'
  
  // 假设日均消耗10个单位
  const dailyConsumption = 10
  const days = Math.floor(current / dailyConsumption)
  return `${days}天`
}

const getSuggestion = (row: any): string => {
  const current = parseInt(row['库存数量']) || 0
  const safe = parseInt(row['安全库存']) || 0
  
  if (current === 0) return '紧急补货'
  if (safe > 0 && current <= safe) return '建议补货'
  if (safe > 0 && current <= safe * 1.2) return '关注库存'
  return '库存充足'
}

const getSuggestionClass = (row: any): string => {
  const suggestion = getSuggestion(row)
  switch (suggestion) {
    case '紧急补货': return 'text-red-600 font-semibold'
    case '建议补货': return 'text-orange-600 font-medium'
    case '关注库存': return 'text-yellow-600'
    default: return 'text-green-600'
  }
}

const getRowClassName = ({ row }: { row: any }): string => {
  const current = parseInt(row['库存数量']) || 0
  const safe = parseInt(row['安全库存']) || 0
  
  if (current === 0) return 'bg-red-50'
  if (safe > 0 && current <= safe) return 'bg-yellow-50'
  return ''
}
</script>

<style scoped>
.inventory-preview-panel {
  max-width: 100%;
}

.preview-summary {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  padding: 20px;
}

.inventory-analysis {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f8fafc;
  font-weight: 600;
}

:deep(.el-table .bg-red-50) {
  background-color: #fef2f2;
}

:deep(.el-table .bg-yellow-50) {
  background-color: #fffbeb;
}
</style> 