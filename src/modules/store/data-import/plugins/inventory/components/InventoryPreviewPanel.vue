<template>
  <div class="inventory-preview-panel">
    <!-- 预览摘要 -->
    <div class="preview-summary mb-6">
      <h3 class="text-xl font-semibold mb-4">
        库存导入预览
        <span v-if="isLoading" class="text-sm text-gray-500 ml-2">加载中...</span>
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div class="text-sm text-blue-600 font-medium">总记录数</div>
          <div class="text-2xl font-bold text-blue-700">{{ validationSummary.total }}</div>
        </div>
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
          <div class="text-sm text-green-600 font-medium">验证通过</div>
          <div class="text-2xl font-bold text-green-700">{{ validationSummary.valid }}</div>
        </div>
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="text-sm text-red-600 font-medium">验证失败</div>
          <div class="text-2xl font-bold text-red-700">{{ validationSummary.invalid }}</div>
        </div>
      </div>
    </div>

    <!-- API预览结果展示区域 -->
    <div v-if="apiPreviewData" class="api-preview-result mb-6">
      <h4 class="text-lg font-semibold mb-4">后端验证结果</h4>
      <div class="bg-gray-50 border rounded-lg p-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div class="text-sm text-blue-600 font-medium">总项目数</div>
            <div class="text-xl font-bold text-blue-700">{{ apiPreviewData.totalItems || 0 }}</div>
                </div>
          <div class="bg-green-50 border border-green-200 rounded-lg p-3">
            <div class="text-sm text-green-600 font-medium">有效项目</div>
            <div class="text-xl font-bold text-green-700">{{ apiPreviewData.validItems || 0 }}</div>
              </div>
          <div class="bg-red-50 border border-red-200 rounded-lg p-3">
            <div class="text-sm text-red-600 font-medium">无效项目</div>
            <div class="text-xl font-bold text-red-700">{{ apiPreviewData.invalidItems || 0 }}</div>
          </div>
        </div>

        <div v-if="apiPreviewData.importToken" class="text-sm text-gray-600">
          <div><strong>导入令牌:</strong> {{ apiPreviewData.importToken }}</div>
          <div v-if="apiPreviewData.tokenExpireTime">
            <strong>令牌过期时间:</strong> {{ formatExpireTime(apiPreviewData.tokenExpireTime) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="inventory-table mb-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-lg font-semibold">导入明细</h4>
        <div class="flex items-center gap-3">
          <el-button 
            v-if="!apiPreviewData" 
            type="primary" 
            @click="showTimeSelectionDialog" 
            :loading="isLoading"
            size="small"
          >
            获取导入预览
          </el-button>
          
          <el-input
            v-model="searchKeyword"
            placeholder="搜索商品ID或名称..."
            size="small"
            style="width: 200px;"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select v-model="filterStatus" placeholder="筛选状态" size="small" style="width: 120px;">
            <el-option label="全部" value="all" />
            <el-option label="验证通过" value="valid" />
            <el-option label="验证失败" value="invalid" />
          </el-select>
        </div>
      </div>

      <!-- 无数据提示 -->
      <div v-if="!apiPreviewData && !isLoading" class="text-center py-12 text-gray-500">
        <div class="text-lg mb-2">📋</div>
        <div>请点击"获取导入预览"按钮获取数据</div>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-else
        :data="filteredData"
        style="width: 100%"
        max-height="400"
        stripe
        border
      >
        <el-table-column label="商品ID" width="150" fixed="left">
          <template #default="{ row }">
            <span class="font-mono text-sm">{{ row.productId || 'N/A' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="商品名称" min-width="200">
          <template #default="{ row }">
            <span class="font-medium">{{ row.productName || 'N/A' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="当前库存" width="100" align="right">
          <template #default="{ row }">
            <span class="text-gray-600">{{ formatNumber(row.currentStock || 0) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="导入库存" width="120" align="right" sortable>
          <template #default="{ row }">
            <span class="font-medium">{{ formatNumber(getStockValue(row)) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getOperationTagType(row)" size="small">
              {{ getOperationText(row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="验证状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getValidationTagType(row)" size="small">
              {{ getValidationText(row) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 数据统计 -->
      <div class="mt-4 text-center text-sm text-gray-500">
        共 {{ filteredData.length }} 条记录
      </div>
    </div>

    <!-- 底部操作 -->
    <div class="flex justify-between items-center">
      <div class="text-sm text-gray-500">
        <span v-if="isLoading">正在获取导入预览数据...</span>
        <span v-else-if="!apiPreviewData">请点击"获取导入预览"按钮获取后端验证结果</span>
        <span v-else>确认数据无误后，点击"确认导入"开始执行库存更新</span>
      </div>
      <div class="space-x-3">
        <el-button @click="handlePrevStep" :disabled="isLoading">
          上一步
        </el-button>
        <el-button type="primary" @click="handleConfirmImport" :disabled="!canConfirmImport" :loading="isLoading">
          确认导入
        </el-button>
      </div>
    </div>

    <!-- 入库时间选择弹窗 -->
    <el-dialog
      v-model="timeDialogVisible"
      title="选择入库时间"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="space-y-4">
        <div class="text-sm text-gray-600 mb-4">
          请选择库存数据的入库时间，这将影响库存记录的时间戳
        </div>
        
        <!-- 时间选择方式切换 -->
        <el-radio-group v-model="timeInputMode" class="mb-4">
          <el-radio value="datetime">选择日期时间</el-radio>
          <el-radio value="timestamp">输入时间戳</el-radio>
        </el-radio-group>

        <!-- 日期时间选择器 -->
        <div v-if="timeInputMode === 'datetime'" class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">入库时间</label>
          <el-date-picker
            v-model="stockTime"
            type="datetime"
            placeholder="选择入库时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%;"
            :disabled-date="disabledDate"
          />
        </div>

        <!-- 时间戳输入 -->
        <div v-else class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">时间戳（秒或毫秒）</label>
          <el-input
            v-model="timestampInput"
            placeholder="请输入时间戳（如：1672531200 或 1672531200000）"
            @input="handleTimestampInput"
          />
          <div v-if="timestampPreview" class="text-xs text-gray-500">
            预览时间：{{ timestampPreview }}
          </div>
        </div>

        <!-- 当前选择的时间显示 -->
        <div v-if="stockTime" class="p-3 bg-blue-50 border border-blue-200 rounded">
          <div class="text-sm text-blue-800">
            <strong>选择的入库时间：</strong>{{ stockTime }}
          </div>
          <div class="text-xs text-blue-600 mt-1">
            时间戳：{{ Math.floor(new Date(stockTime).getTime() / 1000) }}
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end gap-3">
          <el-button @click="timeDialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="confirmTimeAndCallAPI"
            :disabled="!stockTime"
          >
            确认并获取预览
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入确认弹窗 -->
    <ImportConfirmDialog
      v-model="showConfirmDialog"
      :store-name="props.stepData.selectedStoreName || ''"
      :loading="isLoading"
      :import-stats="confirmDialogStats"
      confirm-message="此操作将执行库存数据导入，导入后数据无法撤销，请确认无误后继续。"
      @confirm="executeImport"
      @cancel="showConfirmDialog = false"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessage, ElLoading } from 'element-plus'
import type { ImportStepProps, ImportStepEmits } from '../../../core/types'
import { businessInventoryApi } from '@/api/business/inventory'
import type { StockImportPreviewReqDto, StockImportPreviewVO, StockImportItemDto, StockImportExecuteReqDto } from '@/entities/business/inventory'
import { ImportConfirmDialog } from '../../../core/components/import-confirm-dialog'

// Props 和 Emits
const props = defineProps<ImportStepProps>()
const emit = defineEmits<ImportStepEmits>()

// 响应式数据
const searchKeyword = ref('')
const filterStatus = ref('all')
const isLoading = ref(false)
const apiPreviewData = ref<StockImportPreviewVO | null>(null)
const stockTime = ref('')
const timestampInput = ref('')
const timeDialogVisible = ref(false)
const timeInputMode = ref<'datetime' | 'timestamp'>('datetime')
const showConfirmDialog = ref(false)

// 计算属性 - 时间戳预览
const timestampPreview = computed(() => {
  if (!timestampInput.value) return ''
  const timestamp = parseInt(timestampInput.value)
  if (isNaN(timestamp) || timestamp <= 0) return ''
  
  // 自动判断是秒级还是毫秒级时间戳
  const ms = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp
  const date = new Date(ms)
  if (isNaN(date.getTime())) return ''
  
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
})

// 计算属性 - 获取预览数据（只显示API返回的数据）
const previewData = computed(() => {
  // 只使用API返回的预览数据，不显示本地预览数据
  if (apiPreviewData.value?.previewItems) {
    return apiPreviewData.value.previewItems
  }
  // 如果没有API数据，返回空数组
  return []
})

// 是否可以确认导入 - 必须先获取API预览数据
const canConfirmImport = computed(() => {
  return previewData.value.length > 0 && !isLoading.value && !!apiPreviewData.value
})

// 验证状态统计（只使用API数据）
const validationSummary = computed(() => {
  // 只使用API数据的统计信息
  if (apiPreviewData.value) {
    return {
      total: apiPreviewData.value.totalItems || 0,
      valid: apiPreviewData.value.validItems || 0,
      invalid: apiPreviewData.value.invalidItems || 0
    }
  }
  
  // 如果没有API数据，返回空统计
  return { total: 0, valid: 0, invalid: 0 }
})

// 确认弹窗的统计信息
const confirmDialogStats = computed(() => {
  const summary = validationSummary.value
  return {
    total: summary.total,
    valid: summary.valid,
    invalid: summary.invalid
  }
})

// 过滤后的数据
const filteredData = computed(() => {
  let filtered = previewData.value

  // 搜索过滤
  if (searchKeyword.value) {
    filtered = filtered.filter((row: any) => {
      const keyword = searchKeyword.value.toLowerCase()
      const productId = (row.productId || '').toLowerCase()
      const productName = (row.productName || '').toLowerCase()
      
      return productId.includes(keyword) || productName.includes(keyword)
    })
  }

  // 状态过滤
  if (filterStatus.value !== 'all') {
    filtered = filtered.filter((row: any) => {
      switch (filterStatus.value) {
        case 'valid':
          return row.valid === true || (row.isMatched && row.isValid !== false)
        case 'invalid':
          return row.valid === false || (!row.isMatched || row.isValid === false)
        default:
          return true
      }
    })
  }

  return filtered
})

// 工具方法
const formatNumber = (value: number): string => {
  return value.toLocaleString()
}

const getStockValue = (row: any): number => {
  // 兼容两种数据格式：API返回的stock字段或我们的newStock字段
  return row.stock || row.newStock || 0
}

// API调用方法
const callImportPreviewAPI = async (): Promise<void> => {
  if (!props.stepData.stockImportPreview?.items || !props.stepData.selectedStoreId) {
    console.warn('缺少必要的预览数据或门店信息')
    return
  }

  isLoading.value = true
  
  try {
    console.log('调用库存导入预览API...')
    
    // 构建API请求数据
    const items: StockImportItemDto[] = props.stepData.stockImportPreview.items
      .filter((item: any) => item.isMatched && item.productId) // 只发送匹配成功的商品
      .map((item: any) => ({
        productId: item.productId,
        stock: item.newStock || 0
      }))

    if (items.length === 0) {
      ElMessage.warning('没有有效的商品数据可以预览')
      return
    }

    // 将选择的时间转换为时间戳
    const selectedTime = new Date(stockTime.value).getTime()
    const stockTimestamp = Math.floor(selectedTime / 1000) // 转换为秒级时间戳
    
    const requestData: StockImportPreviewReqDto = {
      venueId: props.stepData.selectedStoreId,
      items,
      ctime: stockTimestamp,
      utime: stockTimestamp
    }

    console.log('API请求参数:', JSON.stringify(requestData, null, 2))
    
    // 调用API
    const response = await businessInventoryApi.previewImportStock(requestData)
    
    console.log('API响应:', response)
    apiPreviewData.value = response
    
    // 更新步骤数据，保存API返回的导入令牌等信息
    emit('update:step-data', {
      ...props.stepData,
      importToken: response.importToken,
      tokenExpireTime: response.tokenExpireTime,
      apiPreviewData: response
    })
    
    ElMessage.success('导入预览数据加载成功')
    
  } catch (error) {
    console.error('导入预览API调用失败:', error)
    ElMessage.error('获取导入预览失败: ' + (error as Error).message)
  } finally {
    isLoading.value = false
  }
}

const getValidationTagType = (row: any): string => {
  // 兼容两种数据格式
  if (row.valid === true || (row.isMatched && row.isValid !== false)) return 'success'
  if (row.valid === false || (!row.isMatched || row.isValid === false)) return 'danger'
  return 'info'
}

const getValidationText = (row: any): string => {
  // 兼容两种数据格式
  if (row.valid === true || (row.isMatched && row.isValid !== false)) return '验证通过'
  if (row.valid === false || (!row.isMatched || row.isValid === false)) return '验证失败'
  return '待验证'
}

const getOperationTagType = (row: any): string => {
  const operation = row.operation || 'update'
  switch (operation.toLowerCase()) {
    case 'update':
      return 'primary'
    case 'reset':
      return 'warning'
    case 'no_change':
      return 'info'
    default:
      return 'primary'
  }
}

const getOperationText = (row: any): string => {
  const operation = row.operation || 'update'
  switch (operation.toLowerCase()) {
    case 'update':
      return '更新'
    case 'reset':
      return '重置'
    case 'no_change':
      return '无变化'
    default:
      return '更新'
  }
}

const formatExpireTime = (timestamp: number): string => {
  // 将时间戳转换为可读的时间格式
  const date = new Date(timestamp * 1000) // API返回的是秒级时间戳
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 时间处理方法
const disabledDate = (time: Date): boolean => {
  // 禁用未来时间，只允许选择当前时间及之前的时间
  return time.getTime() > Date.now()
}

const handleTimestampInput = (value: string): void => {
  const timestamp = parseInt(value)
  if (!isNaN(timestamp) && timestamp > 0) {
    // 如果是秒级时间戳（10位数字），转换为毫秒
    const ms = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp
    const date = new Date(ms)
    if (!isNaN(date.getTime())) {
      // 使用本地时间，避免时区转换
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      stockTime.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }
  }
}

// 弹窗相关方法
const showTimeSelectionDialog = (): void => {
  // 重置时间输入
  if (!stockTime.value) {
    const now = new Date()
    // 使用本地时间，避免时区转换
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')
    const seconds = String(now.getSeconds()).padStart(2, '0')
    stockTime.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }
  timestampInput.value = ''
  timeInputMode.value = 'datetime'
  timeDialogVisible.value = true
}

const confirmTimeAndCallAPI = async (): Promise<void> => {
  if (!stockTime.value) {
    ElMessage.warning('请选择入库时间')
    return
  }
  
  timeDialogVisible.value = false
  await callImportPreviewAPI()
}

// 处理方法
const handlePrevStep = (): void => {
  // 重置获取状态，清空API数据
  apiPreviewData.value = null
  
  // 清空步骤数据中的API相关数据
  emit('update:step-data', {
    ...props.stepData,
    apiPreviewData: null,
    importToken: '',
    tokenExpireTime: null,
    confirmImport: false
  })
  
  emit('prev')
}

const handleConfirmImport = (): void => {
  // 获取门店信息
  const venueId = props.stepData.selectedStoreId
  if (!venueId) {
    ElMessage.error('门店信息缺失，无法执行导入')
    return
  }

  // 获取门店名称（必须通过stepData传递）
  const venueName = props.stepData.selectedStoreName
  
  if (!venueName) {
    // 如果找不到门店名称，报错并阻止导入
    ElMessage.error('门店信息缺失，请重新选择门店后再试')
    console.error('门店名称未找到，门店ID:', venueId, 'stepData:', props.stepData)
    return
  }
  
  // 显示通用确认弹窗
  showConfirmDialog.value = true
}

const executeImport = async (): Promise<void> => {
  if (!apiPreviewData.value?.importToken) {
    ElMessage.error('缺少导入令牌，请重新获取预览数据')
    return
  }

  if (!props.stepData.selectedStoreId) {
    ElMessage.error('门店信息缺失，无法执行导入')
    return
  }

  isLoading.value = true
  
  try {
    console.log('执行库存导入...')
    
    // 构建执行导入的API请求数据
    const executeData: StockImportExecuteReqDto = {
      venueId: props.stepData.selectedStoreId,
      importToken: apiPreviewData.value.importToken
    }

    console.log('执行导入API请求参数:', JSON.stringify(executeData, null, 2))
    
    // 调用执行导入API
    const importResult = await businessInventoryApi.executeImportStock(executeData)
    
    console.log('执行导入API响应:', importResult)
    
    ElMessage.success(`库存导入成功！成功更新 ${importResult.successCount || 0} 个商品的库存`)
    
    // 更新步骤数据，保存导入结果，标记导入已完成
    emit('update:step-data', {
      ...props.stepData,
      confirmImport: true,
      importCompleted: true,
      importResult: importResult,
      apiPreviewData: apiPreviewData.value
    })
    
    // 触发下一步
    emit('next')
    
  } catch (error) {
    console.error('库存导入失败:', error)
    ElMessage.error('库存导入失败: ' + (error as Error).message)
  } finally {
    isLoading.value = false
  }
}

// 生命周期 - 组件挂载时检查是否已有API数据
onMounted(() => {
  // 如果步骤数据中已有API预览数据，则直接使用
  if (props.stepData.apiPreviewData) {
    apiPreviewData.value = props.stepData.apiPreviewData
  }
  
  // 设置默认入库时间为当前时间
  if (!stockTime.value) {
    const now = new Date()
    // 使用本地时间，避免时区转换
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')
    const seconds = String(now.getSeconds()).padStart(2, '0')
    stockTime.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }
  
  // 不自动调用API，等待用户点击按钮
})
</script>

<style scoped>
.inventory-preview-panel {
  max-width: 100%;
}

.preview-summary {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  padding: 20px;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f8fafc;
  font-weight: 600;
}
</style> 