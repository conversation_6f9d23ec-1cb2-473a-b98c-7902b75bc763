import { computed, onMounted, nextTick } from 'vue'
import type { 
  IInventoryValidationViewModel,
  IInventoryValidationState,
  IInventoryValidationComputed,
  IInventoryValidationActions
} from './viewmodel'
import { 
  useInventoryValidationPageState, 
  type InventoryValidationPageStateAPI 
} from './state'
import { useInventoryValidationInteractor } from './interactor'
import { 
  InventoryValidationConverter
} from './converter'
import type { 
  InventoryValidationProps, 
  InventoryValidationEmits,
  ValidationError,
  ValidationResult,
  StepData
} from './type'

/**
 * 库存验证页面Presenter
 * 核心协调者，实现IInventoryValidationViewModel接口
 */
export class InventoryValidationPresenter implements IInventoryValidationViewModel {
  private pageState: InventoryValidationPageStateAPI = useInventoryValidationPageState()
  private interactor = useInventoryValidationInteractor()
  private props?: InventoryValidationProps
  private emit?: InventoryValidationEmits

  // --- IInventoryValidationState 实现 (通过 computed 连接 State 层) ---
  public state: IInventoryValidationState = {
    isLoading: computed(() => this.pageState.state.isLoading),
    activeTab: computed({
      get: (): string => this.pageState.state.activeTab,
      set: (value: string): void => { this.actions.handleTabChange(value) }
    }),
    searchKeyword: computed({
      get: (): string => this.pageState.state.searchKeyword,
      set: (value: string): void => { this.actions.handleSearch(value) }
    }),
    filterStatus: computed({
      get: (): string => this.pageState.state.filterStatus,
      set: (value: string): void => { this.actions.handleFilterChange(value) }
    }),
    hasErrors: computed(() => this.pageState.state.hasErrors),
    hasWarnings: computed(() => this.pageState.state.hasWarnings),
    canProceed: computed(() => this.pageState.state.canProceed)
  }

  // --- IInventoryValidationComputed 实现 (基于响应式 state 计算) ---
  public computed: IInventoryValidationComputed = {
    validationResult: computed(() => {
      const result = this.pageState.state.stepData.validationResult
      if (!result) return undefined
      return {
        ...result,
        errors: [...result.errors],
        warnings: [...result.warnings]
      }
    }),
    parsedData: computed(() => [...(this.pageState.state.stepData.parsedData?.data || [])]),
    totalRows: computed(() => this.computed.parsedData.value.length),
    validRows: computed(() => this.computed.validationResult.value?.validRows || 0),
    errorRows: computed(() => this.computed.errors.value.length),
    warningRows: computed(() => this.computed.warnings.value.length),
    errors: computed(() => this.computed.validationResult.value?.errors || []),
    warnings: computed(() => this.computed.validationResult.value?.warnings || []),
    
    uniqueProducts: computed(() => {
      const productNames = new Set(this.computed.parsedData.value.map((row: any) => row['商品名称']))
      return productNames.size
    }),
    
    totalQuantity: computed(() => {
      return this.computed.parsedData.value.reduce((total: number, row: any) => {
        const quantity = parseInt(row['库存数量']) || 0
        return total + quantity
      }, 0)
    }),
    
    lowStockCount: computed(() => {
      return this.computed.parsedData.value.filter((row: any) => 
        this.actions.isLowStock(row['库存数量'])
      ).length
    }),
    
    previewData: computed(() => this.computed.parsedData.value.slice(0, 20)),
    
    filteredData: computed(() => {
      let filtered = this.computed.parsedData.value

      // 搜索过滤
      const keyword = this.state.searchKeyword.value
      if (keyword) {
        filtered = filtered.filter((row: any) => 
          row['商品名称']?.toString().toLowerCase().includes(keyword.toLowerCase())
        )
      }

      // 状态过滤
      const status = this.state.filterStatus.value
      if (status !== 'all') {
        filtered = filtered.filter((row: any) => {
          const qty = parseInt(row['库存数量']) || 0
          
          switch (status) {
            case 'normal':
              return qty >= 10
            case 'low':
              return qty > 0 && qty < 10
            case 'zero':
              return qty === 0
            default:
              return true
          }
        })
      }

      return filtered
    }),
    

    
    validationSummary: computed(() => ({
      totalRecords: this.computed.totalRows.value,
      validRecords: this.computed.validRows.value,
      errorRecords: this.computed.errorRows.value,
      warningRecords: this.computed.warningRows.value
    })),
    
    inventoryStats: computed(() => ({
      uniqueProducts: this.computed.uniqueProducts.value,
      totalQuantity: this.computed.totalQuantity.value,
      lowStockCount: this.computed.lowStockCount.value,
      zeroStockCount: this.computed.parsedData.value.filter((row: any) => 
        (parseInt(row['库存数量']) || 0) === 0
      ).length
    })),
    
    /** 新增: 库存导入预览项列表 */
    previewItems: computed(() => {
      // 从stepData中获取stockImportPreview数据
      const stockImportPreview = this.pageState.state.stepData.stockImportPreview
      if (stockImportPreview && stockImportPreview.items) {
        return [...stockImportPreview.items] // 创建副本以解决只读问题
      }
      
      // 如果没有预览数据，返回空数组
      return []
    })
  }

  // --- IInventoryValidationActions 实现 (业务流程编排) ---
  public actions: IInventoryValidationActions = {
    // 基础操作
    handlePrevStep: (): void => {
      if (!this.state.isLoading.value) {
        this.emit?.('prev')
      }
    },

    handleNextStep: (): void => {
      if (!this.state.isLoading.value && this.state.canProceed.value) {
        this.emit?.('next')
      }
    },

    handleConfirmWithErrors: async (): Promise<void> => {
      if (this.state.isLoading.value) return

      try {
        // 如果有错误，弹窗确认（只考虑真正的错误，不包括警告）
        if (this.state.hasErrors.value) {
          const unmatchedItems = this.computed.previewItems.value.filter(item => !item.isMatched)
          const errorCount = unmatchedItems.length
          const validCount = this.computed.previewItems.value.filter(item => item.isMatched).length
          
          // 构建错误商品列表
          const errorProductNames = unmatchedItems.slice(0, 5).map(item => item.productName).join('、')
          const hasMoreErrors = unmatchedItems.length > 5
          
          let confirmMessage = `发现 ${errorCount} 个商品不存在，这些商品将被跳过：\n\n`
          confirmMessage += `• ${errorProductNames}`
          if (hasMoreErrors) {
            confirmMessage += `\n• 还有 ${unmatchedItems.length - 5} 个商品...`
          }
          confirmMessage += `\n\n仅导入 ${validCount} 个有效商品，是否继续？`
          
          // 显示确认对话框
          const confirmed = await this.showConfirmDialog(
            '跳过错误继续导入',
            confirmMessage,
            'warning'
          )
          
          if (confirmed) {
            console.log('用户确认跳过错误继续导入')
            
            // 过滤掉验证失败的数据，只保留验证通过的数据
            const currentStepData = this.pageState.state.stepData
            if (currentStepData.validationResult && currentStepData.stockImportPreview) {
              console.log('Presenter: 过滤验证失败的数据，只保留验证通过的数据')
              
              // 过滤出验证通过的预览项（匹配成功的商品，包含有警告但匹配成功的项）
              const validItems = currentStepData.stockImportPreview.items.filter(item => item.isMatched)
              
              // 更新汇总信息
              const filteredSummary = {
                ...currentStepData.stockImportPreview.summary,
                totalItems: validItems.length,
                matchedItems: validItems.length,
                unmatchedItems: 0,
                validItems: validItems.length,
                invalidItems: 0
              }
              
              // 创建过滤后的预览数据
              const filteredStockImportPreview = {
                ...currentStepData.stockImportPreview,
                summary: filteredSummary,
                items: validItems
              }
              
              // 更新验证结果，移除错误记录
              const filteredValidationResult: ValidationResult = {
                ...currentStepData.validationResult,
                isValid: true, // 过滤后应该是有效的
                errors: [], // 清空错误
                warnings: [...currentStepData.validationResult.warnings], // 保留警告
                validRows: validItems.length,
                totalRows: validItems.length
              }
              
              console.log('Presenter: 传递过滤后的验证结果到父组件')
              console.log('Presenter: 过滤前项目数量:', currentStepData.stockImportPreview.items.length)
              console.log('Presenter: 过滤后项目数量:', validItems.length)
              
              this.emit?.('update:step-data', {
                validationResult: filteredValidationResult,
                validationErrors: [], // 清空验证错误
                stockImportPreview: filteredStockImportPreview,
                skipErrors: true // 标记已跳过错误
              })
            }
            
            // 直接触发下一步，不依赖canProceed状态
            this.emit?.('next')
          } else {
            console.log('用户取消导入')
          }
        } else {
          this.emit?.('next')
        }
      } catch (error) {
        console.error('确认导入失败:', error)
        this.pageState.setError((error as Error).message)
      }
    },

    // 搜索和过滤
    handleSearch: (keyword: string): void => {
      this.pageState.setSearchKeyword(keyword)
    },

    handleFilterChange: (status: string): void => {
      this.pageState.setFilterStatus(status)
    },

    handleTabChange: (tab: string): void => {
      this.pageState.setActiveTab(tab)
    },

    // 数据验证 - 合并商品信息和Excel数据
    validateInventoryData: async (): Promise<void> => {
      if (this.state.isLoading.value) return

      this.pageState.setLoading(true)
      this.pageState.clearError()

      try {
        const stepData = this.pageState.state.stepData
        
        console.log('Presenter: 验证库存数据，stepData:', stepData)
        console.log('Presenter: selectedStoreId:', stepData.selectedStoreId, '类型:', typeof stepData.selectedStoreId)
        console.log('Presenter: parsedData存在:', !!stepData.parsedData?.data, '数据长度:', stepData.parsedData?.data?.length)
        
        if (!stepData.parsedData?.data || !stepData.selectedStoreId) {
          throw new Error('缺少必要的验证数据')
        }

        // 1. 基础文件格式验证
        const formatValidation = this.interactor.validateFileFormat([...stepData.parsedData.data])
        if (!formatValidation.isValid) {
          throw new Error(`文件格式错误: ${formatValidation.errors.join(', ')}`)
        }

        // 2. 获取门店商品列表 (ProductItem格式)
        console.log('Presenter: 准备调用 getVenueProductItems，selectedStoreId:', stepData.selectedStoreId)
        const productList = await this.interactor.getVenueProductItems(stepData.selectedStoreId)

        // 3. 合并商品信息和Excel数据 (不调用API预览接口)
        const previewItems = InventoryValidationConverter.mergeProductAndStockData(
          [...stepData.parsedData.data],
          productList,
          stepData.syncMode || 'incremental'
        )

        // 4. 生成预览数据
        const stockImportPreview = InventoryValidationConverter.generateStockImportPreviewData(
          stepData.selectedStoreId,
          previewItems,
          undefined, // 不调用API预览
          stepData.syncMode || 'incremental',
          stepData.effectiveTime
        )

        // 5. 转换为传统的验证结果格式 (兼容现有逻辑)
        const validationResult = InventoryValidationConverter.previewDataToValidationResult(stockImportPreview)

        // 6. 更新状态
        const updatedStepData: Partial<StepData> = {
          validationResult,
          validationErrors: [...validationResult.errors, ...validationResult.warnings],
          stockImportPreview // 新增预览数据
        }
        
        this.pageState.updateStepData(updatedStepData)
        
        console.log('Presenter: Inventory validation completed successfully')
        console.log('Presenter: 生成预览项数量:', previewItems.length)
        console.log('Presenter: 匹配成功项数量:', previewItems.filter(item => item.isMatched).length)
      } catch (error) {
        console.error('Presenter: Inventory validation failed', error)
        this.pageState.setError((error as Error).message)
      } finally {
        this.pageState.setLoading(false)
      }
    },

    // 工具方法
    isLowStock: (currentStock: any): boolean => {
      const current = parseInt(currentStock) || 0
      return current < 10 && current > 0
    },

    getQuantityClass: (currentStock: any): string => {
      const current = parseInt(currentStock) || 0
      if (current === 0) {
        return 'text-red-600 font-bold'
      } else if (current < 10) {
        return 'text-yellow-600 font-semibold'
      }
      return 'text-gray-900'
    },

    hasRowErrors: (rowIndex: number): boolean => {
      return this.computed.errors.value.some((error: ValidationError) => error.row === rowIndex)
    },

    hasRowWarnings: (rowIndex: number): boolean => {
      return this.computed.warnings.value.some((warning: ValidationError) => warning.row === rowIndex)
    },

    /** 新增: 获取库存变化的样式类 */
    getStockChangeClass: (stockChange?: number): string => {
      if (stockChange === undefined || stockChange === null) {
        return 'text-gray-500'
      }
      
      if (stockChange > 0) {
        return 'text-green-600' // 库存增加 - 绿色
      } else if (stockChange < 0) {
        return 'text-red-600'   // 库存减少 - 红色
      } else {
        return 'text-gray-500'  // 无变化 - 灰色
      }
    },

    // Props和Emits设置
    setupProps: (props: InventoryValidationProps): void => {
      this.props = props
      
      // 监听props变化并更新state
      if (props.stepData) {
        this.pageState.setStepData(props.stepData)
        
        // 如果有数据但没有验证结果，自动执行验证
        if (props.stepData.parsedData?.data && !props.stepData.validationResult) {
          nextTick(() => {
            this.actions.validateInventoryData()
          })
        }
      }
    },

    setupEmits: (emit: InventoryValidationEmits): void => {
      this.emit = emit
    }
  }

  // --- 私有方法 ---
  
  /**
   * 显示确认对话框
   * @param title 对话框标题
   * @param message 确认消息
   * @param type 对话框类型
   * @returns 用户是否确认
   */
  private async showConfirmDialog(
    title: string, 
    message: string, 
    type: 'info' | 'success' | 'warning' | 'error' = 'warning'
  ): Promise<boolean> {
    try {
      // 使用 Element Plus 的 MessageBox
      const { ElMessageBox } = await import('element-plus')
      
      await ElMessageBox.confirm(
        message,
        title,
        {
          confirmButtonText: '确认导入',
          cancelButtonText: '取消',
          type,
          dangerouslyUseHTMLString: false,
          beforeClose: (action, instance, done) => {
            done()
          }
        }
      )
      
      return true
    } catch (error) {
      // 用户点击取消或关闭对话框
      return false
    }
  }

  // --- 生命周期钩子 ---
  constructor() {
    onMounted(() => {
      console.log("Presenter: InventoryValidation component mounted")
      
      // 初始化时如果有数据，执行验证
      if (this.pageState.state.stepData.parsedData?.data) {
        this.actions.validateInventoryData()
      }
    })
  }
}

/**
 * Presenter 工厂函数
 */
export function useInventoryValidationPresenter(): IInventoryValidationViewModel {
  return new InventoryValidationPresenter()
} 