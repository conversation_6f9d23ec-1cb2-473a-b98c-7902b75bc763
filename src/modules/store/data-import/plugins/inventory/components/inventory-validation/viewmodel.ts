import type { ComputedRef, WritableComputedRef, Ref } from 'vue'
import type { 
  ValidationError, 
  ValidationResult, 
  InventoryValidationProps, 
  InventoryValidationEmits,
  StockImportPreviewItem 
} from './type'

/**
 * 库存验证页面的视图状态接口 - 基础UI状态
 */
export interface IInventoryValidationState {
  readonly isLoading: ComputedRef<boolean>
  readonly activeTab: WritableComputedRef<string>
  readonly searchKeyword: WritableComputedRef<string>
  readonly filterStatus: WritableComputedRef<string>
  readonly hasErrors: ComputedRef<boolean>
  readonly hasWarnings: ComputedRef<boolean>
  readonly canProceed: ComputedRef<boolean>
}

/**
 * 库存验证页面的计算属性接口 - 派生UI状态
 */
export interface IInventoryValidationComputed {
  readonly validationResult: ComputedRef<ValidationResult | undefined>
  readonly parsedData: ComputedRef<any[]>
  readonly totalRows: ComputedRef<number>
  readonly validRows: ComputedRef<number>
  readonly errorRows: ComputedRef<number>
  readonly warningRows: ComputedRef<number>
  readonly errors: ComputedRef<ValidationError[]>
  readonly warnings: ComputedRef<ValidationError[]>
  readonly uniqueProducts: ComputedRef<number>
  readonly totalQuantity: ComputedRef<number>
  readonly lowStockCount: ComputedRef<number>
  readonly previewData: ComputedRef<any[]>
  readonly filteredData: ComputedRef<any[]>
  /** 新增: 库存导入预览项列表 */
  readonly previewItems: ComputedRef<StockImportPreviewItem[]>
  readonly validationSummary: ComputedRef<{
    totalRecords: number
    validRecords: number
    errorRecords: number
    warningRecords: number
  }>
  readonly inventoryStats: ComputedRef<{
    uniqueProducts: number
    totalQuantity: number
    lowStockCount: number
    zeroStockCount: number
  }>
}

/**
 * 库存验证页面的操作接口
 */
export interface IInventoryValidationActions {
  // 基础操作
  handlePrevStep(): void
  handleNextStep(): void
  handleConfirmWithErrors(): Promise<void>
  
  // 搜索和过滤
  handleSearch(keyword: string): void
  handleFilterChange(status: string): void
  handleTabChange(tab: string): void
  

  
  // 数据验证
  validateInventoryData(): Promise<void>
  
  // 工具方法
  isLowStock(currentStock: any): boolean
  getQuantityClass(currentStock: any): string
  hasRowErrors(rowIndex: number): boolean
  hasRowWarnings(rowIndex: number): boolean
  /** 新增: 获取库存变化的样式类 */
  getStockChangeClass(stockChange?: number): string
  
  // Props和Emits设置
  setupProps(props: InventoryValidationProps): void
  setupEmits(emit: InventoryValidationEmits): void
}

/**
 * 组合的库存验证视图模型接口
 */
export interface IInventoryValidationViewModel {
  state: IInventoryValidationState
  computed: IInventoryValidationComputed
  actions: IInventoryValidationActions
} 