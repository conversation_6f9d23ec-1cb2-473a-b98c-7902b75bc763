<template>
  <div class="inventory-validation-panel">
    <!-- 验证摘要 -->
    <div class="validation-summary mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div class="text-sm text-blue-600 font-medium">总记录数</div>
          <div class="text-2xl font-bold text-blue-700">{{ totalRows }}</div>
        </div>
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
          <div class="text-sm text-green-600 font-medium">有效记录</div>
          <div class="text-2xl font-bold text-green-700">{{ validRows }}</div>
        </div>
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="text-sm text-red-600 font-medium">错误记录</div>
          <div class="text-2xl font-bold text-red-700">{{ errorRows }}</div>
        </div>
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div class="text-sm text-yellow-600 font-medium">警告记录</div>
          <div class="text-2xl font-bold text-yellow-700">{{ warningRows }}</div>
        </div>
      </div>
    </div>

    <!-- 库存数据统计 -->
    <div class="inventory-stats mb-6">
      <h3 class="text-lg font-semibold mb-4">库存数据统计</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="bg-gray-50 border rounded-lg p-4">
          <div class="text-sm text-gray-600">商品总数</div>
          <div class="text-xl font-bold text-gray-800">{{ uniqueProducts }}</div>
        </div>
        <div class="bg-gray-50 border rounded-lg p-4">
          <div class="text-sm text-gray-600">总库存量</div>
          <div class="text-xl font-bold text-gray-800">{{ totalQuantity }}</div>
        </div>
        <div class="bg-gray-50 border rounded-lg p-4">
          <div class="text-sm text-gray-600">低库存商品</div>
          <div class="text-xl font-bold" :class="lowStockCount > 0 ? 'text-red-600' : 'text-gray-800'">
            {{ lowStockCount }}
          </div>
        </div>
      </div>
    </div>

    <!-- 错误和警告详情 -->
    <div v-if="hasErrors || hasWarnings" class="validation-details mb-6">
      <h3 class="text-lg font-semibold mb-4">验证详情</h3>
      
      <!-- 错误选项卡 -->
      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane v-if="hasErrors" label="错误" name="errors">
          <div class="space-y-4">
            <div 
              v-for="(error, index) in errors" 
              :key="`error-${index}`"
              class="border border-red-200 rounded-lg p-4 bg-red-50"
            >
              <div class="flex items-start gap-3">
                <el-icon class="text-red-500 mt-1"><WarningFilled /></el-icon>
                <div class="flex-1">
                  <div class="text-sm font-medium text-red-700">
                    第 {{ error.row + 1 }} 行 - {{ error.field }}
                  </div>
                  <div class="text-sm text-red-600 mt-1">{{ error.message }}</div>
                  <div v-if="error.value" class="text-xs text-gray-500 mt-1">
                    当前值: {{ error.value }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane v-if="hasWarnings" label="警告" name="warnings">
          <div class="space-y-4">
            <div 
              v-for="(warning, index) in warnings" 
              :key="`warning-${index}`"
              class="border border-yellow-200 rounded-lg p-4 bg-yellow-50"
            >
              <div class="flex items-start gap-3">
                <el-icon class="text-yellow-500 mt-1"><Warning /></el-icon>
                <div class="flex-1">
                  <div class="text-sm font-medium text-yellow-700">
                    第 {{ warning.row + 1 }} 行 - {{ warning.field }}
                  </div>
                  <div class="text-sm text-yellow-600 mt-1">{{ warning.message }}</div>
                  <div v-if="warning.value" class="text-xs text-gray-500 mt-1">
                    当前值: {{ warning.value }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 库存数据预览表格 -->
    <div class="data-preview">
      <h3 class="text-lg font-semibold mb-4">数据预览</h3>
      <el-table 
        :data="previewData" 
        style="width: 100%"
        max-height="400"
        stripe
        border
      >
        <el-table-column prop="商品编号" label="商品编号" width="120" />
        <el-table-column prop="库存数量" label="库存数量" width="100" align="right">
          <template #default="{ row }">
            <span :class="getQuantityClass(row['库存数量'], row['安全库存'])">
              {{ row['库存数量'] }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="安全库存" label="安全库存" width="100" align="right" />
        <el-table-column label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag 
              v-if="isLowStock(row['库存数量'], row['安全库存'])"
              type="danger" 
              size="small"
            >
              低库存
            </el-tag>
            <el-tag 
              v-else
              type="success" 
              size="small"
            >
              正常
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="验证状态" width="100" align="center">
          <template #default="{ row, $index }">
            <el-icon 
              v-if="hasRowErrors($index)"
              class="text-red-500"
            >
              <CircleCloseFilled />
            </el-icon>
            <el-icon 
              v-else-if="hasRowWarnings($index)"
              class="text-yellow-500"
            >
              <WarningFilled />
            </el-icon>
            <el-icon 
              v-else
              class="text-green-500"
            >
              <CircleCheckFilled />
            </el-icon>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 底部操作按钮 -->
    <div class="mt-6 flex justify-between items-center">
      <div class="text-sm text-gray-500">
        {{ hasErrors ? '请修复所有错误后继续' : '验证通过，可以继续导入' }}
      </div>
      <div class="space-x-3">
        <el-button @click="$emit('prev')">
          上一步
        </el-button>
        <el-button 
          type="primary" 
          @click="$emit('next')"
          :disabled="hasErrors"
        >
          下一步
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { WarningFilled, Warning, CircleCloseFilled, CircleCheckFilled } from '@element-plus/icons-vue'
import type { ImportStepProps, ImportStepEmits } from '../../../core/types'

// Props 和 Emits
const props = defineProps<ImportStepProps>()
const emit = defineEmits<ImportStepEmits>()

// 响应式数据
const activeTab = ref('errors')

// 计算属性
const validationResult = computed(() => props.stepData.validationResult)
const parsedData = computed(() => props.stepData.parsedData?.data || [])

const totalRows = computed(() => parsedData.value.length)
const validRows = computed(() => validationResult.value?.validRows || 0)
const errorRows = computed(() => validationResult.value?.errors?.length || 0)
const warningRows = computed(() => validationResult.value?.warnings?.length || 0)

const errors = computed(() => validationResult.value?.errors || [])
const warnings = computed(() => validationResult.value?.warnings || [])

const hasErrors = computed(() => errors.value.length > 0)
const hasWarnings = computed(() => warnings.value.length > 0)

// 库存统计
const uniqueProducts = computed(() => {
  const productCodes = new Set(parsedData.value.map((row: any) => row['商品编号']))
  return productCodes.size
})

const totalQuantity = computed(() => {
  return parsedData.value.reduce((total: number, row: any) => {
    const quantity = parseInt(row['库存数量']) || 0
    return total + quantity
  }, 0)
})

const lowStockCount = computed(() => {
  return parsedData.value.filter((row: any) => 
    isLowStock(row['库存数量'], row['安全库存'])
  ).length
})

// 预览数据（取前20条）
const previewData = computed(() => parsedData.value.slice(0, 20))

// 方法
const isLowStock = (currentStock: any, safeStock: any): boolean => {
  const current = parseInt(currentStock) || 0
  const safe = parseInt(safeStock) || 0
  return safe > 0 && current <= safe
}

const getQuantityClass = (currentStock: any, safeStock: any): string => {
  if (isLowStock(currentStock, safeStock)) {
    return 'text-red-600 font-semibold'
  }
  return 'text-gray-900'
}

const hasRowErrors = (rowIndex: number): boolean => {
  return errors.value.some((error: any) => error.row === rowIndex)
}

const hasRowWarnings = (rowIndex: number): boolean => {
  return warnings.value.some((warning: any) => warning.row === rowIndex)
}
</script>

<style scoped>
.inventory-validation-panel {
  max-width: 100%;
}

.validation-summary {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  padding: 20px;
}

.inventory-stats {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f8fafc;
  font-weight: 600;
}
</style> 