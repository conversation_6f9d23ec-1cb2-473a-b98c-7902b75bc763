<template>
  <div class="inventory-import-result-panel">
    <!-- 导入结果摘要 -->
    <div class="result-summary mb-6">
      <el-result
        :icon="isSuccess ? 'success' : 'warning'"
        :title="resultTitle"
        :sub-title="resultSubTitle"
      >
        <template #extra>
          <!-- 统计卡片 -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
              <div class="text-3xl font-bold text-blue-700 mb-1">{{ importResult.totalCount || 0 }}</div>
              <div class="text-sm text-blue-600 font-medium">总记录数</div>
            </div>
            <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
              <div class="text-3xl font-bold text-green-700 mb-1">{{ importResult.successCount || 0 }}</div>
              <div class="text-sm text-green-600 font-medium">成功导入</div>
            </div>
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
              <div class="text-3xl font-bold text-red-700 mb-1">{{ importResult.failedCount || 0 }}</div>
              <div class="text-sm text-red-600 font-medium">导入失败</div>
            </div>
          </div>

          <!-- 导入详情 -->
          <div class="bg-gray-50 border rounded-lg p-4 mb-6">
            <h4 class="text-lg font-semibold mb-3">导入详情</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-600">门店ID:</span>
                <span class="ml-2 font-medium">{{ importResult.venueId || 'N/A' }}</span>
              </div>
              <div>
                <span class="text-gray-600">门店名称:</span>
                <span class="ml-2 font-medium">{{ props.stepData.selectedStoreName || 'N/A' }}</span>
              </div>
              <div>
                <span class="text-gray-600">导入时间:</span>
                <span class="ml-2 font-medium">{{ formatImportTime(importResult.importTime) }}</span>
              </div>
              <div>
                <span class="text-gray-600">成功率:</span>
                <span class="ml-2 font-medium" :class="successRateClass">{{ successRate }}%</span>
              </div>
            </div>
            <div v-if="importResult.message" class="mt-3 pt-3 border-t border-gray-200">
              <span class="text-gray-600">系统消息:</span>
              <span class="ml-2 font-medium text-gray-800">{{ importResult.message }}</span>
            </div>
          </div>

          <!-- 失败项目详情 -->
          <div v-if="hasFailedItems" class="failed-items mb-6">
            <h4 class="text-lg font-semibold mb-3 text-red-700">
              <el-icon class="mr-1"><Warning /></el-icon>
              导入失败项目 ({{ importResult.failedCount || 0 }} 项)
            </h4>
            <div class="border border-red-200 rounded-lg overflow-hidden">
              <el-table
                :data="importResult.failedItems || []"
                style="width: 100%"
                max-height="300"
                stripe
              >
                <el-table-column label="商品ID" width="150">
                  <template #default="{ row }">
                    <span class="font-mono text-sm">{{ row.productId || 'N/A' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="库存数量" width="120" align="right">
                  <template #default="{ row }">
                    <span class="font-medium">{{ formatNumber(row.stock || 0) }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="失败原因" min-width="300">
                  <template #default="{ row }">
                    <div class="text-red-600">
                      <el-tooltip :content="row.reason" placement="top" :disabled="!row.reason || row.reason.length < 50">
                        <span class="cursor-help">{{ truncateReason(row.reason) }}</span>
                      </el-tooltip>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex gap-3 justify-center">
            <el-button @click="handleDownloadReport" :disabled="isLoading">
              <el-icon class="mr-1"><Download /></el-icon>
              下载导入报告
            </el-button>
            <el-button @click="handleRetryImport" :disabled="isLoading">
              <el-icon class="mr-1"><Refresh /></el-icon>
              重新导入
            </el-button>
            <el-button type="primary" @click="handleBackToList">
              <el-icon class="mr-1"><Back /></el-icon>
              返回列表
            </el-button>
          </div>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { Warning, Download, Refresh, Back } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { ImportStepProps, ImportStepEmits } from '../../../core/types'
import type { StockImportExecuteVO } from '@/entities/business/inventory'
import { useDataImportState } from '../../../stores/dataImportState'

// Props 和 Emits
const props = defineProps<ImportStepProps>()
const emit = defineEmits<ImportStepEmits>()

const router = useRouter()
const isLoading = ref(false)
const dataImportState = useDataImportState()

// 计算属性
const importResult = computed(() => {
  return (props.stepData.importResult as StockImportExecuteVO) || {}
})

const isSuccess = computed(() => {
  const total = importResult.value.totalCount || 0
  const success = importResult.value.successCount || 0
  return total > 0 && success === total
})

const resultTitle = computed(() => {
  if (isSuccess.value) {
    return '库存导入成功'
  } else if ((importResult.value.successCount || 0) > 0) {
    return '库存导入部分成功'
  } else {
    return '库存导入失败'
  }
})

const resultSubTitle = computed(() => {
  const total = importResult.value.totalCount || 0
  const success = importResult.value.successCount || 0
  const failed = importResult.value.failedCount || 0
  
  if (total === 0) {
    return '没有数据需要导入'
  } else if (failed === 0) {
    return `成功导入 ${success} 个商品的库存数据`
  } else {
    return `成功导入 ${success} 个商品，${failed} 个商品导入失败`
  }
})

const successRate = computed(() => {
  const total = importResult.value.totalCount || 0
  const success = importResult.value.successCount || 0
  if (total === 0) return 0
  return Math.round((success / total) * 100)
})

const successRateClass = computed(() => {
  const rate = successRate.value
  if (rate === 100) return 'text-green-600'
  if (rate >= 80) return 'text-yellow-600'
  return 'text-red-600'
})

const hasFailedItems = computed(() => {
  return (importResult.value.failedItems?.length || 0) > 0
})

// 工具方法
const formatNumber = (num: number): string => {
  return num.toLocaleString()
}

const formatImportTime = (timestamp?: number): string => {
  if (!timestamp) return 'N/A'
  const date = new Date(timestamp * 1000) // 假设是秒级时间戳
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const truncateReason = (reason?: string): string => {
  if (!reason) return 'N/A'
  return reason.length > 50 ? reason.substring(0, 50) + '...' : reason
}

// 事件处理方法
const handleDownloadReport = (): void => {
  isLoading.value = true
  
  try {
    // 生成导入报告数据
    const reportData = {
      importTime: formatImportTime(importResult.value.importTime),
      storeName: props.stepData.selectedStoreName || 'N/A',
      storeId: importResult.value.venueId || 'N/A',
      totalCount: importResult.value.totalCount || 0,
      successCount: importResult.value.successCount || 0,
      failedCount: importResult.value.failedCount || 0,
      successRate: successRate.value,
      message: importResult.value.message || '',
      failedItems: importResult.value.failedItems || []
    }
    
    // 生成CSV格式的报告
    let csvContent = '库存导入报告\n\n'
    csvContent += `导入时间,${reportData.importTime}\n`
    csvContent += `门店名称,${reportData.storeName}\n`
    csvContent += `门店ID,${reportData.storeId}\n`
    csvContent += `总记录数,${reportData.totalCount}\n`
    csvContent += `成功导入,${reportData.successCount}\n`
    csvContent += `导入失败,${reportData.failedCount}\n`
    csvContent += `成功率,${reportData.successRate}%\n`
    csvContent += `系统消息,${reportData.message}\n\n`
    
    if (reportData.failedItems.length > 0) {
      csvContent += '失败项目详情\n'
      csvContent += '商品ID,库存数量,失败原因\n'
      reportData.failedItems.forEach(item => {
        csvContent += `${item.productId || 'N/A'},${item.stock || 0},"${(item.reason || 'N/A').replace(/"/g, '""')}"\n`
      })
    }
    
    // 下载文件
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `库存导入报告_${reportData.storeName}_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('导入报告下载成功')
  } catch (error) {
    console.error('下载报告失败:', error)
    ElMessage.error('下载报告失败')
  } finally {
    isLoading.value = false
  }
}

const handleRetryImport = (): void => {
  // 强制重置当前会话
  dataImportState.resetCurrentSession()
  
  // 重新开始导入流程 - 这会触发新的会话创建
  router.push({ 
    name: 'ImportWizard', 
    params: { pluginId: props.plugin?.id || 'inventory' } 
  })
}

const handleBackToList = (): void => {
  // 强制重置当前会话
  dataImportState.resetCurrentSession()
  
  router.push('/store/data-import')
}
</script>

<style scoped>
.inventory-import-result-panel {
  max-width: 100%;
}

.result-summary {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  padding: 20px;
}

:deep(.el-result__title) {
  margin-top: 10px;
  font-size: 1.5rem;
  font-weight: 600;
}

:deep(.el-result__subtitle) {
  margin-top: 8px;
  font-size: 1rem;
  color: #64748b;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f8fafc;
  font-weight: 600;
}
</style> 