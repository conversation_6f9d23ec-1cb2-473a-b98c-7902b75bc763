<template>
  <div class="wine-storage-validation-panel">
    <!-- 验证摘要 -->
    <div class="validation-summary mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div class="text-sm text-blue-600 font-medium">总记录数</div>
          <div class="text-2xl font-bold text-blue-700">{{ totalRows }}</div>
        </div>
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
          <div class="text-sm text-green-600 font-medium">有效记录</div>
          <div class="text-2xl font-bold text-green-700">{{ validRows }}</div>
        </div>
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="text-sm text-red-600 font-medium">错误记录</div>
          <div class="text-2xl font-bold text-red-700">{{ errorRows }}</div>
        </div>
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div class="text-sm text-yellow-600 font-medium">警告记录</div>
          <div class="text-2xl font-bold text-yellow-700">{{ warningRows }}</div>
        </div>
      </div>
    </div>

    <!-- 存酒数据统计 -->
    <div class="wine-storage-stats mb-6">
      <h3 class="text-lg font-semibold mb-4">存酒数据统计</h3>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <div class="text-sm text-purple-600">会员总数</div>
          <div class="text-xl font-bold text-purple-800">{{ uniqueMembers }}</div>
        </div>
        <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
          <div class="text-sm text-indigo-600">酒品种类</div>
          <div class="text-xl font-bold text-indigo-800">{{ uniqueWines }}</div>
        </div>
        <div class="bg-amber-50 border border-amber-200 rounded-lg p-4">
          <div class="text-sm text-amber-600">总存储量</div>
          <div class="text-xl font-bold text-amber-800">{{ totalStorageQuantity }}</div>
        </div>
        <div class="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
          <div class="text-sm text-emerald-600">平均存储</div>
          <div class="text-xl font-bold text-emerald-800">{{ averageStorage }}</div>
        </div>
      </div>
    </div>

    <!-- 热门酒品统计 -->
    <div class="popular-wines mb-6">
      <h3 class="text-lg font-semibold mb-4">热门存酒品种</h3>
      <div class="bg-white border rounded-lg p-4">
        <div class="space-y-3">
          <div 
            v-for="(wine, index) in topWines" 
            :key="wine.name"
            class="flex items-center justify-between"
          >
            <div class="flex items-center gap-3">
              <div 
                class="w-8 h-8 rounded-full flex items-center justify-center text-white font-semibold text-sm"
                :class="getWineRankingColor(index)"
              >
                {{ index + 1 }}
              </div>
              <span class="font-medium">{{ wine.name }}</span>
            </div>
            <div class="flex items-center gap-4">
              <span class="text-sm text-gray-600">{{ wine.count }} 条记录</span>
              <span class="font-semibold">{{ wine.quantity }} 瓶</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误和警告详情 -->
    <div v-if="hasErrors || hasWarnings" class="validation-details mb-6">
      <h3 class="text-lg font-semibold mb-4">验证详情</h3>
      
      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane v-if="hasErrors" label="错误" name="errors">
          <div class="space-y-4">
            <div 
              v-for="(error, index) in errors" 
              :key="`error-${index}`"
              class="border border-red-200 rounded-lg p-4 bg-red-50"
            >
              <div class="flex items-start gap-3">
                <el-icon class="text-red-500 mt-1"><WarningFilled /></el-icon>
                <div class="flex-1">
                  <div class="text-sm font-medium text-red-700">
                    第 {{ error.row + 1 }} 行 - {{ error.field }}
                  </div>
                  <div class="text-sm text-red-600 mt-1">{{ error.message }}</div>
                  <div v-if="error.value" class="text-xs text-gray-500 mt-1">
                    当前值: {{ error.value }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane v-if="hasWarnings" label="警告" name="warnings">
          <div class="space-y-4">
            <div 
              v-for="(warning, index) in warnings" 
              :key="`warning-${index}`"
              class="border border-yellow-200 rounded-lg p-4 bg-yellow-50"
            >
              <div class="flex items-start gap-3">
                <el-icon class="text-yellow-500 mt-1"><Warning /></el-icon>
                <div class="flex-1">
                  <div class="text-sm font-medium text-yellow-700">
                    第 {{ warning.row + 1 }} 行 - {{ warning.field }}
                  </div>
                  <div class="text-sm text-yellow-600 mt-1">{{ warning.message }}</div>
                  <div v-if="warning.value" class="text-xs text-gray-500 mt-1">
                    当前值: {{ warning.value }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 存酒数据预览表格 -->
    <div class="data-preview">
      <h3 class="text-lg font-semibold mb-4">数据预览</h3>
      <el-table 
        :data="previewData" 
        style="width: 100%"
        max-height="400"
        stripe
        border
      >
        <el-table-column prop="会员编号" label="会员编号" width="120" />
        <el-table-column prop="酒品名称" label="酒品名称" min-width="150">
          <template #default="{ row }">
            <div class="flex items-center gap-2">
              <el-icon class="text-amber-600"><Wine /></el-icon>
              <span>{{ row['酒品名称'] }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="存储数量" label="存储数量" width="100" align="right">
          <template #default="{ row }">
            <span class="font-semibold">{{ formatNumber(row['存储数量']) }}</span>
            <span class="text-xs text-gray-500 ml-1">瓶</span>
          </template>
        </el-table-column>
        <el-table-column label="存储类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="getStorageTypeTag(row['存储数量'])"
              size="small"
            >
              {{ getStorageTypeText(row['存储数量']) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="验证状态" width="100" align="center">
          <template #default="{ row, $index }">
            <el-icon 
              v-if="hasRowErrors($index)"
              class="text-red-500"
            >
              <CircleCloseFilled />
            </el-icon>
            <el-icon 
              v-else-if="hasRowWarnings($index)"
              class="text-yellow-500"
            >
              <WarningFilled />
            </el-icon>
            <el-icon 
              v-else
              class="text-green-500"
            >
              <CircleCheckFilled />
            </el-icon>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 底部操作按钮 -->
    <div class="mt-6 flex justify-between items-center">
      <div class="text-sm text-gray-500">
        {{ hasErrors ? '请修复所有错误后继续' : '验证通过，可以继续导入' }}
      </div>
      <div class="space-x-3">
        <el-button @click="$emit('prev')">
          上一步
        </el-button>
        <el-button 
          type="primary" 
          @click="$emit('next')"
          :disabled="hasErrors"
        >
          下一步
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { WarningFilled, Warning, CircleCloseFilled, CircleCheckFilled, Wine } from '@element-plus/icons-vue'
import type { ImportStepProps, ImportStepEmits } from '../../../core/types'

// Props 和 Emits
const props = defineProps<ImportStepProps>()
const emit = defineEmits<ImportStepEmits>()

// 响应式数据
const activeTab = ref('errors')

// 计算属性
const validationResult = computed(() => props.stepData.validationResult)
const parsedData = computed(() => props.stepData.parsedData?.data || [])

const totalRows = computed(() => parsedData.value.length)
const validRows = computed(() => validationResult.value?.validRows || 0)
const errorRows = computed(() => validationResult.value?.errors?.length || 0)
const warningRows = computed(() => validationResult.value?.warnings?.length || 0)

const errors = computed(() => validationResult.value?.errors || [])
const warnings = computed(() => validationResult.value?.warnings || [])

const hasErrors = computed(() => errors.value.length > 0)
const hasWarnings = computed(() => warnings.value.length > 0)

// 存酒统计
const uniqueMembers = computed(() => {
  const members = new Set(parsedData.value.map((row: any) => row['会员编号']))
  return members.size
})

const uniqueWines = computed(() => {
  const wines = new Set(parsedData.value.map((row: any) => row['酒品名称']))
  return wines.size
})

const totalStorageQuantity = computed(() => {
  return parsedData.value.reduce((total: number, row: any) => {
    return total + (parseInt(row['存储数量']) || 0)
  }, 0)
})

const averageStorage = computed(() => {
  if (totalRows.value === 0) return 0
  return Math.round(totalStorageQuantity.value / totalRows.value * 10) / 10
})

// 热门酒品统计
const topWines = computed(() => {
  const wineStats = new Map<string, { count: number; quantity: number }>()
  
  parsedData.value.forEach((row: any) => {
    const wineName = row['酒品名称']
    const quantity = parseInt(row['存储数量']) || 0
    
    if (wineName) {
      if (!wineStats.has(wineName)) {
        wineStats.set(wineName, { count: 0, quantity: 0 })
      }
      const stats = wineStats.get(wineName)!
      stats.count++
      stats.quantity += quantity
    }
  })
  
  return Array.from(wineStats.entries())
    .map(([name, stats]) => ({ name, ...stats }))
    .sort((a, b) => b.quantity - a.quantity)
    .slice(0, 5)
})

// 预览数据（取前20条）
const previewData = computed(() => parsedData.value.slice(0, 20))

// 方法
const formatNumber = (value: any): string => {
  const num = parseInt(value) || 0
  return num.toLocaleString()
}

const getStorageTypeTag = (quantity: any): string => {
  const qty = parseInt(quantity) || 0
  if (qty >= 10) return 'danger'  // 大量存储
  if (qty >= 5) return 'warning'   // 中等存储
  if (qty > 0) return 'success'    // 少量存储
  return 'info'                    // 无存储
}

const getStorageTypeText = (quantity: any): string => {
  const qty = parseInt(quantity) || 0
  if (qty >= 10) return '大量'
  if (qty >= 5) return '中等'
  if (qty > 0) return '少量'
  return '无'
}

const getWineRankingColor = (index: number): string => {
  const colors = [
    'bg-yellow-500',   // 金色第1名
    'bg-gray-400',     // 银色第2名
    'bg-amber-600',    // 铜色第3名
    'bg-blue-500',     // 蓝色第4名
    'bg-green-500'     // 绿色第5名
  ]
  return colors[index] || 'bg-gray-400'
}

const hasRowErrors = (rowIndex: number): boolean => {
  return errors.value.some((error: any) => error.row === rowIndex)
}

const hasRowWarnings = (rowIndex: number): boolean => {
  return warnings.value.some((warning: any) => warning.row === rowIndex)
}
</script>

<style scoped>
.wine-storage-validation-panel {
  max-width: 100%;
}

.validation-summary {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  padding: 20px;
}

.wine-storage-stats {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

.popular-wines {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f8fafc;
  font-weight: 600;
}
</style> 