<template>
  <div class="space-y-6">
    <div>
      <h3 class="text-lg font-semibold text-gray-900 mb-4">导入结果</h3>
      
      <el-result
        :icon="importResult?.success ? 'success' : 'error'"
        :title="importResult?.success ? '导入成功' : '导入失败'"
        :sub-title="importResult?.summary || '数据导入完成'"
      >
        <template #extra>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600">{{ importResult?.totalRows || 0 }}</div>
              <div class="text-sm text-gray-600">总记录数</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600">{{ importResult?.successRows || 0 }}</div>
              <div class="text-sm text-gray-600">成功导入</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-red-600">{{ importResult?.errorRows || 0 }}</div>
              <div class="text-sm text-gray-600">导入失败</div>
            </div>
          </div>
          
          <!-- 错误信息 -->
          <div v-if="importResult?.errors && importResult.errors.length > 0" class="mb-6">
            <h4 class="font-medium text-gray-900 mb-3">导入错误详情</h4>
            <div class="border border-red-200 rounded-lg max-h-60 overflow-y-auto">
              <div v-for="error in importResult.errors" :key="`${error.row}-${error.field}`"
                   class="p-3 border-b border-red-100 last:border-b-0">
                <div class="flex items-start gap-3">
                  <el-icon class="text-red-500 mt-0.5"><Warning /></el-icon>
                  <div>
                    <p class="text-sm font-medium text-red-700">
                      第 {{ error.row + 1 }} 行，字段 "{{ error.field }}"
                    </p>
                    <p class="text-sm text-red-600">{{ error.message }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="flex gap-3 justify-center">
            <el-button @click="resetWizard">重新导入</el-button>
            <el-button type="primary" @click="backToList">返回列表</el-button>
          </div>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { Warning } from '@element-plus/icons-vue'
import type { ImportStepProps, ImportStepEmits, ImportResult } from '../../core/types'

interface Props extends ImportStepProps {}
interface Emits extends ImportStepEmits {}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const router = useRouter()

const importResult = computed(() => props.stepData.importResult as ImportResult)

const resetWizard = () => {
  emit('update:step-data', {
    selectedStoreId: '',
    selectedStore: null,
    uploadedFile: null,
    parsedData: null,
    validationErrors: [],
    importResult: null
  })
  // 重置到第一步
  router.push({ params: { pluginId: props.plugin?.id } })
}

const backToList = () => {
  router.push('/store/data-import')
}
</script> 