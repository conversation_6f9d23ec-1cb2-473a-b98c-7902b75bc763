// 数据导入插件管理器

import type { 
  DataImportPlugin, 
  PluginManager, 
  PluginState, 
  PluginRegistryConfig 
} from './types'

/**
 * 插件管理器实现
 */
export class DataImportPluginManager implements PluginManager {
  private plugins: Map<string, DataImportPlugin> = new Map()
  private pluginStates: Map<string, PluginState> = new Map()
  private config: PluginRegistryConfig

  constructor(config: PluginRegistryConfig) {
    this.config = config
  }

  /**
   * 注册插件
   */
  register(plugin: DataImportPlugin): void {
    try {
      // 验证插件
      this.validatePlugin(plugin)
      
      // 检查是否已存在
      if (this.plugins.has(plugin.id)) {
        throw new Error(`Plugin ${plugin.id} is already registered`)
      }

      // 注册插件
      this.plugins.set(plugin.id, plugin)
      
      // 设置插件状态
      this.pluginStates.set(plugin.id, {
        id: plugin.id,
        status: 'installed',
        lastUpdated: Date.now()
      })

      // 执行安装钩子
      if (plugin.onInstall) {
        plugin.onInstall()
      }

      // 如果插件默认启用，则启用它
      if (plugin.enabled && !this.config.disabledPlugins.includes(plugin.id)) {
        this.enable(plugin.id)
      }

      console.log(`Plugin ${plugin.id} registered successfully`)
    } catch (error) {
      console.error(`Failed to register plugin ${plugin.id}:`, error)
      this.pluginStates.set(plugin.id, {
        id: plugin.id,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        lastUpdated: Date.now()
      })
    }
  }

  /**
   * 注销插件
   */
  unregister(pluginId: string): void {
    const plugin = this.plugins.get(pluginId)
    if (!plugin) {
      console.warn(`Plugin ${pluginId} not found`)
      return
    }

    try {
      // 如果插件已启用，先禁用
      const state = this.pluginStates.get(pluginId)
      if (state?.status === 'enabled') {
        this.disable(pluginId)
      }

      // 执行卸载钩子
      if (plugin.onUninstall) {
        plugin.onUninstall()
      }

      // 移除插件
      this.plugins.delete(pluginId)
      this.pluginStates.delete(pluginId)

      console.log(`Plugin ${pluginId} unregistered successfully`)
    } catch (error) {
      console.error(`Failed to unregister plugin ${pluginId}:`, error)
    }
  }

  /**
   * 启用插件
   */
  enable(pluginId: string): void {
    const plugin = this.plugins.get(pluginId)
    if (!plugin) {
      throw new Error(`Plugin ${pluginId} not found`)
    }

    const state = this.pluginStates.get(pluginId)
    if (state?.status === 'enabled') {
      return // 已经启用
    }

    try {
      // 执行启用钩子
      if (plugin.onEnable) {
        plugin.onEnable()
      }

      // 更新状态
      this.pluginStates.set(pluginId, {
        id: pluginId,
        status: 'enabled',
        lastUpdated: Date.now()
      })

      console.log(`Plugin ${pluginId} enabled successfully`)
    } catch (error) {
      console.error(`Failed to enable plugin ${pluginId}:`, error)
      this.pluginStates.set(pluginId, {
        id: pluginId,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        lastUpdated: Date.now()
      })
    }
  }

  /**
   * 禁用插件
   */
  disable(pluginId: string): void {
    const plugin = this.plugins.get(pluginId)
    if (!plugin) {
      throw new Error(`Plugin ${pluginId} not found`)
    }

    const state = this.pluginStates.get(pluginId)
    if (state?.status === 'disabled') {
      return // 已经禁用
    }

    try {
      // 执行禁用钩子
      if (plugin.onDisable) {
        plugin.onDisable()
      }

      // 更新状态
      this.pluginStates.set(pluginId, {
        id: pluginId,
        status: 'disabled',
        lastUpdated: Date.now()
      })

      console.log(`Plugin ${pluginId} disabled successfully`)
    } catch (error) {
      console.error(`Failed to disable plugin ${pluginId}:`, error)
      this.pluginStates.set(pluginId, {
        id: pluginId,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        lastUpdated: Date.now()
      })
    }
  }

  /**
   * 获取插件
   */
  getPlugin(pluginId: string): DataImportPlugin | null {
    return this.plugins.get(pluginId) || null
  }

  /**
   * 获取所有插件
   */
  getAllPlugins(): DataImportPlugin[] {
    return Array.from(this.plugins.values())
  }

  /**
   * 获取已启用的插件
   */
  getEnabledPlugins(): DataImportPlugin[] {
    return Array.from(this.plugins.values()).filter(plugin => {
      const state = this.pluginStates.get(plugin.id)
      return state?.status === 'enabled'
    })
  }

  /**
   * 获取插件状态
   */
  getPluginState(pluginId: string): PluginState | null {
    return this.pluginStates.get(pluginId) || null
  }

  /**
   * 获取所有插件状态
   */
  getAllPluginStates(): PluginState[] {
    return Array.from(this.pluginStates.values())
  }

  /**
   * 验证插件
   */
  private validatePlugin(plugin: DataImportPlugin): void {
    if (!plugin.id) {
      throw new Error('Plugin must have an id')
    }

    if (!plugin.name) {
      throw new Error('Plugin must have a name')
    }

    if (!plugin.displayName) {
      throw new Error('Plugin must have a displayName')
    }

    if (!plugin.version) {
      throw new Error('Plugin must have a version')
    }

    if (!plugin.templateGenerator) {
      throw new Error('Plugin must have a templateGenerator')
    }

    if (!plugin.dataValidator) {
      throw new Error('Plugin must have a dataValidator')
    }

    if (!plugin.dataPreviewer) {
      throw new Error('Plugin must have a dataPreviewer')
    }

    if (!plugin.dataImporter) {
      throw new Error('Plugin must have a dataImporter')
    }
  }

  /**
   * 重新加载插件
   */
  reload(pluginId: string): void {
    const plugin = this.plugins.get(pluginId)
    if (!plugin) {
      throw new Error(`Plugin ${pluginId} not found`)
    }

    // 重新注册插件
    this.unregister(pluginId)
    this.register(plugin)
  }

  /**
   * 清理所有插件
   */
  cleanup(): void {
    const pluginIds = Array.from(this.plugins.keys())
    pluginIds.forEach(id => this.unregister(id))
  }
}

/**
 * 全局插件管理器实例
 */
export const pluginManager = new DataImportPluginManager({
  autoDiscover: true,
  pluginPaths: [],
  enabledPlugins: [],
  disabledPlugins: []
}) 