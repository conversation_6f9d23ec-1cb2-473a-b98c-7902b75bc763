<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <div class="bg-white border-b border-gray-200 sticky top-0 z-10">
      <div class="max-w-7xl mx-auto px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <el-button 
              @click="router.push('/store/data-import')" 
              type="text" 
              :icon="ArrowLeft"
            >
              返回
            </el-button>
            <div class="h-6 w-px bg-gray-300"></div>
            <div class="flex items-center gap-2">
              <el-icon class="text-blue-600" :size="20">
                <component :is="currentPlugin?.icon || 'Upload'" />
              </el-icon>
              <h1 class="text-xl font-semibold text-gray-900">
                {{ currentPlugin?.displayName || '数据导入' }}
              </h1>
            </div>
          </div>
          
          <!-- 步骤指示器 -->
          <div class="flex items-center gap-4">
            <div 
              v-for="(step, index) in steps" 
              :key="step.key"
              class="flex items-center gap-2"
            >
              <div 
                :class="[
                  'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium',
                  index <= currentStep 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 text-gray-500'
                ]"
              >
                {{ index + 1 }}
              </div>
              <span 
                :class="[
                  'text-sm font-medium',
                  index <= currentStep ? 'text-gray-900' : 'text-gray-500'
                ]"
              >
                {{ step.title }}
              </span>
              <div 
                v-if="index < steps.length - 1" 
                class="w-8 h-px bg-gray-200 ml-2"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="max-w-7xl mx-auto px-6 py-8">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 min-h-[600px]">
        <div class="p-6">
          <!-- 动态步骤内容 -->
          <component 
            :is="currentStepComponent" 
            v-bind="currentStepProps"
            @update:step-data="handleStepDataUpdate"
            @next="nextStep"
            @prev="prevStep"
            @complete="handleComplete"
          />
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="bg-white border-t border-gray-200 py-4 sticky bottom-0">
      <div class="max-w-7xl mx-auto px-6">
        <div class="flex justify-between items-center">
          <div>
            <el-button 
              v-if="currentStep > 0 && !isLastStep" 
              @click="prevStep"
              :disabled="isProcessing"
            >
              上一步
            </el-button>
          </div>
          <div class="flex gap-3">
            <el-button 
              v-if="!isLastStep" 
              type="primary" 
              @click="nextStep"
              :disabled="!canProceed || isProcessing"
              :loading="isProcessing"
            >
              {{ isProcessing ? '处理中...' : '下一步' }}
            </el-button>
            <el-button 
              v-if="isLastStep" 
              type="primary" 
              @click="handleComplete"
              :disabled="!canProceed || isProcessing"
              :loading="isProcessing"
            >
              {{ isProcessing ? '导入中...' : '完成导入' }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, markRaw } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeft, Upload } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

import { getPlugin } from '../index'
import type { DataImportPlugin } from '../core/types'

// 默认步骤组件
import StepSelectStore from '../components/steps/StepSelectStore.vue'
import StepDownloadTemplate from '../components/steps/StepDownloadTemplate.vue'
import StepUploadFile from '../components/steps/StepUploadFile.vue'
import StepValidatePreview from '../components/steps/StepValidatePreview.vue'
import StepImportResult from '../components/steps/StepImportResult.vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const currentStep = ref(0)
const currentPlugin = ref<DataImportPlugin | null>(null)
const isProcessing = ref(false)
const stepData = ref<Record<string, any>>({})

// 默认步骤配置
const defaultSteps = [
  { key: 'select-store', title: '选择门店', component: markRaw(StepSelectStore) },
  { key: 'download-template', title: '下载模板', component: markRaw(StepDownloadTemplate) },
  { key: 'upload-file', title: '上传文件', component: markRaw(StepUploadFile) },
  { key: 'validate-preview', title: '验证预览', component: markRaw(StepValidatePreview) },
  { key: 'import-result', title: '导入结果', component: markRaw(StepImportResult) }
]

// 计算属性
const steps = computed(() => {
  if (!currentPlugin.value) return defaultSteps
  
  // 如果插件定义了自定义步骤，使用插件的步骤
  if (currentPlugin.value.componentConfig?.customSteps) {
    return currentPlugin.value.componentConfig.customSteps.map(step => ({
      ...step,
      component: markRaw(step.component)
    }))
  }
  
  // 否则使用默认步骤，但可能替换某些组件
  return defaultSteps.map(step => {
    const customComponents = currentPlugin.value?.customComponents
    
    switch (step.key) {
      case 'validate-preview':
        if (customComponents?.validationPanel && currentPlugin.value?.componentConfig?.useCustomValidationPreview) {
          return { ...step, component: markRaw(customComponents.validationPanel) }
        }
        break
      case 'import-result':
        if (customComponents?.resultPanel && currentPlugin.value?.componentConfig?.useCustomResultPage) {
          return { ...step, component: markRaw(customComponents.resultPanel) }
        }
        break
    }
    
    return step
  })
})

const isLastStep = computed(() => currentStep.value === steps.value.length - 1)

const currentStepComponent = computed(() => {
  return steps.value[currentStep.value]?.component
})

const currentStepProps = computed(() => {
  return {
    plugin: currentPlugin.value,
    stepData: stepData.value,
    currentStep: currentStep.value,
    isProcessing: isProcessing.value
  }
})

const canProceed = computed(() => {
  const step = steps.value[currentStep.value]
  if (step.canProceed) {
    return step.canProceed(stepData.value)
  }
  
  // 默认逻辑
  switch (step.key) {
    case 'select-store':
      return !!stepData.value.selectedStoreId
    case 'download-template':
      return true
    case 'upload-file':
      return !!stepData.value.uploadedFile && !!stepData.value.parsedData
    case 'validate-preview':
      return !stepData.value.validationErrors?.length
    case 'import-result':
      return true
    default:
      return true
  }
})

// 方法
const loadPlugin = () => {
  const pluginId = route.params.pluginId as string
  currentPlugin.value = getPlugin(pluginId)
  if (!currentPlugin.value) {
    ElMessage.error('插件不存在')
    router.push('/store/data-import')
    return
  }
}

const handleStepDataUpdate = (data: Record<string, any>) => {
  stepData.value = { ...stepData.value, ...data }
}

const nextStep = async () => {
  if (currentStep.value < steps.value.length - 1) {
    isProcessing.value = true
    
    try {
      // 执行当前步骤的完成逻辑
      await executeStepCompletion()
      currentStep.value++
    } catch (error) {
      ElMessage.error('操作失败：' + (error as Error).message)
    } finally {
      isProcessing.value = false
    }
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const executeStepCompletion = async () => {
  const step = steps.value[currentStep.value]
  
  switch (step.key) {
    case 'upload-file':
      // 执行数据验证
      if (currentPlugin.value && stepData.value.parsedData) {
        const validationResult = currentPlugin.value.dataValidator.validate(stepData.value.parsedData.data)
        stepData.value.validationResult = validationResult
        stepData.value.validationErrors = validationResult.errors
      }
      break
    case 'validate-preview':
      // 准备导入
      if (currentPlugin.value) {
        await currentPlugin.value.beforeImport?.(stepData.value.parsedData?.data || [])
      }
      break
  }
}

const handleComplete = async () => {
  if (!currentPlugin.value || !stepData.value.parsedData || !stepData.value.selectedStoreId) {
    ElMessage.error('数据不完整，无法导入')
    return
  }
  
  isProcessing.value = true
  
  try {
    const validData = stepData.value.parsedData.data.filter((_: any, index: number) => 
      !stepData.value.validationErrors?.some((error: any) => error.row === index)
    )
    
    const result = await currentPlugin.value.dataImporter.import(
      validData,
      stepData.value.selectedStoreId
    )
    
    stepData.value.importResult = result
    
    await currentPlugin.value.afterImport?.(result)
    
    if (result.success) {
      ElMessage.success('数据导入成功')
    } else {
      ElMessage.error('数据导入失败')
    }
    
    // 如果不是最后一步，跳转到结果页
    if (!isLastStep.value) {
      currentStep.value = steps.value.length - 1
    }
  } catch (error) {
    ElMessage.error('导入过程中发生错误：' + (error as Error).message)
  } finally {
    isProcessing.value = false
  }
}

onMounted(() => {
  loadPlugin()
})
</script> 