<template>
  <div class="audit-list-container">
    <!-- 页面标题区 -->
    <div class="mb-4 sm:mb-6 flex flex-col sm:flex-row justify-between sm:items-center">
      <h2 class="text-xl sm:text-2xl font-bold mb-3 sm:mb-0">门店审核</h2>
      <el-button type="primary" @click="vm.actions.handleBatchAudit" :disabled="!vm.computes.hasSelected.value">
        批量审核
      </el-button>
    </div>

    <!-- 筛选条件区 -->
    <div class="filter-bar bg-white p-3 sm:p-4 rounded-lg shadow-sm mb-4 sm:mb-6">
      <el-form :inline="true" :model="vm.state.filters" class="flex flex-col sm:flex-row flex-wrap gap-3 sm:gap-4">
        <!-- 时间范围 -->
        <el-form-item label="申请时间" class="w-full sm:w-auto">
          <el-date-picker
            v-model="vm.state.filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="vm.actions.handleDateRangeChange"
            style="width: 100%"
          />
        </el-form-item>

        <!-- 审核状态 -->
        <el-form-item label="审核状态" class="w-full sm:w-auto">
          <el-select 
            v-model="vm.state.filters.status"
            placeholder="请选择状态"
            @change="vm.actions.handleStatusChange"
            style="width: 100%; min-width: 5.5rem;"
          >
            <el-option label="全部" value="all" />
            <el-option label="待审核" value="pending" />
            <el-option label="已通过" value="approved" />
            <el-option label="已拒绝" value="rejected" />
          </el-select>
        </el-form-item>

        <!-- 关键词搜索 -->
        <el-form-item label="关键词" class="w-full sm:w-auto">
          <el-input
            v-model="vm.state.filters.searchKey"
            placeholder="门店名称/联系人/手机号"
            @input="vm.actions.handleSearchInput"
            style="width: 100%"
          >
            <template #suffix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item class="flex justify-center sm:justify-start w-full sm:w-auto">
          <el-button type="primary" @click="vm.actions.handleSearch">查询</el-button>
          <el-button @click="vm.actions.handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 列表内容区 -->
    <div class="audit-list">
      <!-- 加载状态 -->
      <div v-if="vm.state.loading" class="flex justify-center items-center py-12">
        <el-skeleton :rows="3" animated />
      </div>

      <!-- 空状态 -->
      <el-empty v-else-if="vm.computes.isEmpty.value" description="暂无数据" />

      <!-- 卡片列表 -->
      <div v-else class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-3 sm:gap-4">
        <div v-for="item in vm.state.list" :key="item.id" class="audit-card">
          <el-card 
            :class="{ 'is-selected': vm.state.selectedIds.includes(item.id) }"
            @click="vm.actions.handleCardClick(item.id)"
          >
            <!-- 卡片头部：门店名称 + 状态标签 -->
            <template #header>
              <div class="flex justify-between items-center">
                <span class="font-bold text-sm sm:text-base truncate mr-2">{{ item.storeName }}</span>
                <el-tag :type="vm.computes.getStatusType(item.status)" size="small" class="whitespace-nowrap">
                  {{ vm.computes.getStatusText(item.status) }}
                </el-tag>
              </div>
            </template>

            <!-- 卡片内容：基本信息 -->
            <div class="space-y-2 text-xs sm:text-sm">
              <div class="flex justify-between">
                <span class="text-gray-500">申请类型</span>
                <span>{{ item.applyType === 'new' ? '新店申请' : '续期申请' }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">申请时间</span>
                <span>{{ vm.computes.formatDate(item.appliedAt) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">联系电话</span>
                <span>{{ item.contactPhone }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">所在地区</span>
                <span class="truncate max-w-[150px]">{{ item.location.province }} {{ item.location.city }}</span>
              </div>
            </div>

            <!-- 卡片底部：操作按钮 -->
            <div class="mt-3 sm:mt-4 flex flex-wrap justify-end space-x-2">
              <el-button 
                type="primary" 
                size="small"
                @click.stop="vm.actions.handleDetail(item.id)"
              >
                查看详情
              </el-button>
              <el-button 
                v-if="item.status === 'pending'"
                type="success" 
                size="small"
                @click.stop="vm.actions.handleAudit(item.id, 'approve')"
              >
                通过
              </el-button>
              <el-button 
                v-if="item.status === 'pending'"
                type="danger" 
                size="small"
                @click.stop="vm.actions.handleAudit(item.id, 'reject')"
              >
                拒绝
              </el-button>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 分页控制器 -->
      <div class="mt-4 sm:mt-6 flex justify-center">
        <el-pagination
          v-model:current-page="vm.state.pagination.current"
          v-model:page-size="vm.state.pagination.pageSize"
          :total="vm.state.pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next"
          @size-change="vm.actions.handleSizeChange"
          @current-change="vm.actions.handleCurrentChange"
          class="pagination-responsive"
        />
      </div>
    </div>

    <!-- 批量审核弹窗 -->
    <el-dialog
      v-model="vm.state.batchDialog.visible"
      title="批量审核"
      width="95%"
      max-width="500px"
    >
      <el-form :model="vm.state.batchDialog.form" label-width="80px">
        <el-form-item label="审核结果">
          <el-radio-group v-model="vm.state.batchDialog.form.action">
            <el-radio label="approve">通过</el-radio>
            <el-radio label="reject">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注模板">
          <el-select 
            v-model="vm.state.batchDialog.form.templateId"
            placeholder="请选择备注模板"
            style="width: 100%"
          >
            <el-option
              v-for="template in vm.state.templates"
              :key="template.id"
              :label="template.name"
              :value="template.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="审核备注">
          <el-input
            v-model="vm.state.batchDialog.form.remark"
            type="textarea"
            rows="3"
            placeholder="请输入审核备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="vm.state.batchDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="vm.actions.handleBatchConfirm">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'
import { useAuditListPresenter } from './presenter'
import type { IAuditListViewModel } from './viewmodel'

const vm: IAuditListViewModel = useAuditListPresenter()
</script>

<style scoped>
.audit-card :deep(.el-card) {
  transition: all 0.3s;
  cursor: pointer;
}

.audit-card :deep(.el-card):hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.audit-card :deep(.el-card.is-selected) {
  border: 2px solid var(--el-color-primary);
}

/* 移动端优化 */
@media (max-width: 640px) {
  .pagination-responsive :deep(.el-pagination__sizes),
  .pagination-responsive :deep(.el-pagination__jump) {
    display: none !important;
  }
  
  .pagination-responsive {
    width: 100%;
    justify-content: center;
  }
}
</style> 