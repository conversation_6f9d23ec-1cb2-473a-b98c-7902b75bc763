import type { IAuditListState } from './viewmodel'
import type { VenueVO, VenuePageVO } from '@/entities/store/venue'
import type { AuditListQuery, AuditTemplate, AuditListResponse } from './interactor'
import dayjs from 'dayjs'

export class AuditListConverter {
  /**
   * 创建初始状态
   */
  static createInitialState(): IAuditListState {
    return {
      list: [],
      loading: false,
      selectedIds: [],
      filters: {
        dateRange: null,
        status: 'pending',
        searchKey: ''
      },
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      templates: [],
      batchDialog: {
        visible: false,
        form: {
          action: 'approve',
          templateId: '',
          remark: ''
        }
      }
    }
  }

  /**
   * 将筛选条件转换为查询参数
   */
  static toQueryParams(state: IAuditListState): AuditListQuery {
    const { filters, pagination } = state
    return {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      status: this.convertStatusToNumber(filters.status),
      keyword: filters.searchKey || undefined,
      startTime: filters.dateRange?.[0]?.toISOString(),
      endTime: filters.dateRange?.[1]?.toISOString()
    }
  }

  /**
   * 将服务端实体转换为视图实体
   */
  static toAuditItem(vo: VenueVO) {
    return {
      id: vo.id,
      venue_id: vo.id,
      storeName: vo.name,
      applyType: 'new' as const,  // 暂时固定为new
      status: this.convertStatus(vo.auditStatus),
      appliedAt: new Date(vo.ctime * 1000).toISOString(), // 使用创建时间作为申请时间，服务端ctime是Unix时间戳（秒）
      location: {
        province: vo.province,
        city: vo.city,
        district: vo.district
      },
      contactPhone: vo.contactPhone
    }
  }

  /**
   * 将列表响应数据转换为视图状态
   */
  static toListState(state: IAuditListState, response: VenuePageVO): void {
    state.list = response.data.map((item: VenueVO) => this.toAuditItem(item))
    state.pagination.total = response.total
  }

  /**
   * 将模板数据转换为视图状态
   */
  static toTemplatesState(state: IAuditListState, templates: AuditTemplate[]): void {
    // 只更新模板列表
    state.templates = templates.map(template => this.toTemplateOption(template))
  }

  /**
   * 更新加载状态
   */
  static updateLoadingState(state: IAuditListState, loading: boolean): void {
    state.loading = loading
  }

  /**
   * 更新批量审核弹窗状态
   */
  static updateBatchDialogState(
    state: IAuditListState, 
    visible: boolean,
    form?: Partial<typeof state.batchDialog.form>
  ): void {
    state.batchDialog.visible = visible
    if (form) {
      Object.assign(state.batchDialog.form, form)
    }
  }

  /**
   * 更新选中状态
   */
  static updateSelectedState(state: IAuditListState, selectedIds: string[]): void {
    state.selectedIds = selectedIds
  }

  /**
   * 重置筛选条件
   */
  static resetFilters(state: IAuditListState): void {
    state.filters = {
      dateRange: null,
      status: '',
      searchKey: ''
    }
    state.pagination.current = 1
  }

  /**
   * 将模板数据转换为视图实体
   */
  private static toTemplateOption(template: AuditTemplate) {
    return {
      id: template.id,
      name: template.name,
      content: template.content
    }
  }

  /**
   * 格式化日期
   */
  static formatDate(date: string): string {
    return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
  }

  /**
   * 获取状态类型
   */
  static getStatusType(status: string): string {
    const typeMap: Record<string, string> = {
      pending: 'warning',
      approved: 'success',
      rejected: 'danger'
    }
    return typeMap[status] || 'info'
  }

  /**
   * 获取状态文本
   */
  static getStatusText(status: string): string {
    const textMap: Record<string, string> = {
      pending: '待审核',
      approved: '已通过',
      rejected: '已拒绝'
    }
    return textMap[status] || '未知状态'
  }

  /**
   * 转换状态值（字符串 -> 数字）
   */
  private static convertStatusToNumber(status: string): number | undefined {
    if (!status) {
      return undefined
    }
    const statusMap: Record<string, number> = {
      'pending': 0,
      'approved': 1,
      'rejected': 2
    }
    return statusMap[status]
  }

  /**
   * 转换状态值（数字 -> 字符串）
   */
  private static convertStatus(status: number): string {
    const statusMap: Record<number, string> = {
      0: 'pending',
      1: 'approved',
      2: 'rejected'
    }
    return statusMap[status] || 'pending'
  }
}

export function useAuditListConverter() {
  return AuditListConverter
} 