import type { ComputedRef } from 'vue'

// 审核项数据结构
export interface AuditItem {
  id: string
  venue_id: string
  storeName: string
  applyType: 'new' | 'renew'
  status: string
  appliedAt: string
  location: {
    province: string
    city: string
    district: string
  }
  contactPhone: string
}

// 备注模板数据结构
export interface Template {
  id: string
  name: string
  content: string
}

// UI状态接口
export interface IAuditListState {
  // 列表数据
  list: AuditItem[]
  loading: boolean
  selectedIds: string[]
  
  // 筛选条件
  filters: {
    dateRange: [Date, Date] | null
    status: string
    searchKey: string
  }
  
  // 分页信息
  pagination: {
    current: number
    pageSize: number
    total: number
  }

  // 备注模板
  templates: Template[]

  // 批量审核弹窗
  batchDialog: {
    visible: boolean
    form: {
      action: 'approve' | 'reject'
      templateId: string
      remark: string
    }
  }
}

// UI计算属性接口
export interface IAuditListComputed {
  // 是否有选中项
  hasSelected: ComputedRef<boolean>
  // 是否为空列表
  isEmpty: ComputedRef<boolean>
  // 获取状态类型
  getStatusType: (status: string) => string
  // 获取状态文本
  getStatusText: (status: string) => string
  // 格式化日期
  formatDate: (date: string) => string
}

// UI动作接口
export interface IAuditListActions {
  // 筛选相关
  handleDateRangeChange: () => void
  handleStatusChange: () => void
  handleSearchInput: () => void
  handleSearch: () => void
  handleReset: () => void
  
  // 分页相关
  handleSizeChange: (size: number) => void
  handleCurrentChange: (page: number) => void
  
  // 卡片操作
  handleCardClick: (id: string) => void
  handleDetail: (id: string) => void
  handleAudit: (id: string, action: 'approve' | 'reject') => void
  
  // 批量操作
  handleBatchAudit: () => void
  handleBatchConfirm: () => Promise<void>
}

// 组合接口
export interface IAuditListViewModel {
  state: IAuditListState
  computes: IAuditListComputed
  actions: IAuditListActions
} 