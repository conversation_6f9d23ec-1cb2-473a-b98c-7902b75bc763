import type { ComputedRef } from 'vue'

// 视图层门店类型
export interface VenueView {
  id: string                // 门店ID
  name: string             // 门店名称
  type: number            // 门店类型：1-量贩式 2-商务KTV 3-酒吧 4-其他
  address: string         // 门店地址
  contact: string         // 联系人
  phone: string          // 联系电话
}

// 视图层授权状态类型
export interface AuthStatusView {
  status: number          // 授权状态：0-未授权 1-试用中 2-正式授权 3-已过期
  expireTime: string      // 到期时间
  remainDays: number      // 剩余天数
  authType: number        // 授权类型：1-试用授权 2-正式授权
  contractNo?: string     // 合同编号
  contractName?: string   // 合同名称
  lastAuthTime?: string   // 最近授权时间
  remark?: string        // 备注信息
}

// 筛选条件状态
export interface IFilterState {
  keyword: string
  status: string[]
  sortField: string
  sortOrder: 'asc' | 'desc'
}

// 表单数据类型
export interface IFormData {
  days: number           // 有效期天数
  contractNo?: string    // 合同编号（可选）
  contractName?: string  // 合同名称（可选）
  remark: string        // 备注信息
}

// UI 状态接口
export interface ILicenseState {
  venueList: VenueView[]                    // 门店列表
  authStatusMap: Record<string, AuthStatusView> // 门店授权状态映射
  currentVenueId: string | null           // 当前选中的门店ID
  filter: IFilterState                    // 筛选条件
  loading: boolean                        // 加载状态
  formData: IFormData                    // 表单数据
}

// UI 计算属性接口
export interface ILicenseComputed {
  filteredVenueList: ComputedRef<VenueView[]>           // 过滤后的门店列表
  currentVenue: ComputedRef<VenueView | null>           // 当前选中的门店
  currentAuthStatus: ComputedRef<AuthStatusView | null> // 当前门店的授权状态
  recommendDays: ComputedRef<number>                   // 推荐的有效期天数
  canGenerate: ComputedRef<boolean>                    // 是否可以生成授权码
  hasExpiredVenues: ComputedRef<boolean>              // 是否有过期门店
  hasExpiringVenues: ComputedRef<boolean>             // 是否有即将过期门店
  
  // 辅助计算属性
  statusClass: ComputedRef<(status: number) => string>  // 获取状态样式类名
  statusText: ComputedRef<(status: number) => string>   // 获取状态文本
  tagType: ComputedRef<(status: number) => string>      // 获取Tag类型
  venueTypeText: ComputedRef<(type: number) => string>  // 获取门店类型文本
}

// UI 动作接口
export interface ILicenseActions {
  // 列表操作
  selectVenue(venueId: string): void                   // 选择门店
  updateFilter(filter: Partial<IFilterState>): void    // 更新筛选条件
  refreshList(): Promise<void>                         // 刷新列表数据
  
  // 表单操作
  updateFormData(data: Partial<IFormData>): void      // 更新表单数据
  resetForm(): void                                    // 重置表单
  generateAuth(): Promise<void>                        // 生成授权码
  forceExpire(venueId: string): Promise<void>         // 强制失效
  
  // 批量操作
  exportAuthList(): Promise<void>                      // 导出授权列表
  notifyExpiringVenues(): Promise<void>                // 通知即将过期门店
}

// 组合接口
export interface ILicenseViewModel {
  state: ILicenseState
  computes: ILicenseComputed
  actions: ILicenseActions
} 