import { reactive, computed, ComputedRef } from 'vue'
import { ElMessageBox } from 'element-plus'
import type { ILicenseViewModel, ILicenseState, VenueView, AuthStatusView } from './viewmodel'
import { LicenseConverter } from './converter'
import { useLicenseInteractor } from './interactor'

interface IComputes {
  filteredVenueList: ComputedRef<VenueView[]>
  currentVenue: ComputedRef<VenueView | null>
  currentAuthStatus: ComputedRef<AuthStatusView | null>
  recommendDays: ComputedRef<number>
  canGenerate: ComputedRef<boolean>
  hasExpiredVenues: ComputedRef<boolean>
  hasExpiringVenues: ComputedRef<boolean>
  statusClass: ComputedRef<(status: number) => string>
  statusText: ComputedRef<(status: number) => string>
  tagType: ComputedRef<(status: number) => string>
  venueTypeText: ComputedRef<(type: number) => string>
}

/**
 * 授权管理模块展示层
 */
export class LicensePresenter implements ILicenseViewModel {
  private interactor = useLicenseInteractor()
  public state: ILicenseState

  constructor() {
    // 初始化状态
    this.state = reactive(LicenseConverter.createInitialState())
    // 设置生命周期
    this.setupLifecycles()
  }

  /**
   * 计算属性
   */
  public computes: IComputes = {
    // 过滤后的门店列表
    filteredVenueList: computed(() => {
      let list = [...this.state.venueList]
      
      // 关键词过滤
      if (this.state.filter.keyword) {
        list = list.filter(venue => 
          venue.name.toLowerCase().includes(this.state.filter.keyword.toLowerCase()) ||
          venue.address.toLowerCase().includes(this.state.filter.keyword.toLowerCase())
        )
      }

      // 状态过滤
      if (this.state.filter.status.length > 0) {
        list = list.filter(venue => {
          const status = this.state.authStatusMap[venue.id]
          if (!status) return false

          return this.state.filter.status.some(filterStatus => {
            switch (filterStatus) {
              case 'expired':
                return status.status === 3
              case 'expiring':
                return status.status === 1 && status.remainDays <= 15
              case 'valid':
                return status.status === 2
              default:
                return false
            }
          })
        })
      }

      // 优先级排序
      list.sort((a, b) => {
        const statusA = this.state.authStatusMap[a.id]
        const statusB = this.state.authStatusMap[b.id]
        if (!statusA || !statusB) return 0
        
        const priorityA = LicenseConverter.getPriority(statusA)
        const priorityB = LicenseConverter.getPriority(statusB)
        return this.state.filter.sortOrder === 'asc' 
          ? priorityA - priorityB 
          : priorityB - priorityA
      })

      return list
    }),

    // 当前选中的门店
    currentVenue: computed(() => {
      if (!this.state.currentVenueId) return null
      return this.state.venueList.find(v => v.id === this.state.currentVenueId) || null
    }),

    // 当前门店的授权状态
    currentAuthStatus: computed(() => {
      if (!this.state.currentVenueId) return null
      return this.state.authStatusMap[this.state.currentVenueId] || null
    }),

    // 推荐的有效期天数
    recommendDays: computed(() => {
      const status = this.computes.currentAuthStatus.value
      return LicenseConverter.getRecommendDays(status)
    }),

    // 是否可以生成授权码
    canGenerate: computed(() => {
      if (!this.state.currentVenueId) return false
      if (!this.state.formData.days) return false
      if (this.state.formData.days < 7 || this.state.formData.days > 365) return false
      return true
    }),

    // 是否有过期门店
    hasExpiredVenues: computed(() => {
      return Object.values(this.state.authStatusMap).some(status => status.status === 3)
    }),

    // 是否有即将过期门店
    hasExpiringVenues: computed(() => {
      return Object.values(this.state.authStatusMap).some(status => 
        status.status === 1 && status.remainDays <= 15
      )
    }),

    // 状态样式类名
    statusClass: computed(() => (status: number): string => {
      switch (status) {
        case 3: // 已过期
          return 'bg-red-100 text-red-600'
        case 2: // 正式授权
          return 'bg-green-100 text-green-600'
        case 1: // 试用中
          return 'bg-blue-100 text-blue-600'
        default:
          return 'bg-gray-100 text-gray-600'
      }
    }),

    // 状态文本
    statusText: computed(() => (status: number): string => {
      switch (status) {
        case 3:
          return '已过期'
        case 2:
          return '正式授权'
        case 1:
          return '试用中'
        default:
          return '未授权'
      }
    }),

    // Tag类型
    tagType: computed(() => (status: number): string => {
      switch (status) {
        case 3: // 已过期
          return 'danger'
        case 2: // 正式授权
          return 'success'
        case 1: // 试用中
          return 'warning'
        default:
          return 'info'
      }
    }),

    // 门店类型文本
    venueTypeText: computed(() => (type: number): string => {
      switch (type) {
        case 1:
          return '量贩式'
        case 2:
          return '商务KTV'
        case 3:
          return '酒吧'
        default:
          return '其他'
      }
    })
  }

  /**
   * 动作方法
   */
  public actions = {
    // 选择门店
    selectVenue: (venueId: string) => {
      this.state.currentVenueId = venueId
      // 重置表单数据
      this.state.formData = LicenseConverter.createEmptyFormData()
    },

    // 更新筛选条件
    updateFilter: (filter: Partial<ILicenseState['filter']>) => {
      Object.assign(this.state.filter, filter)
    },

    // 刷新列表数据
    refreshList: async () => {
      try {
        this.state.loading = true
        // 获取门店列表
        const venues = await this.interactor.getVenueList()
        // 更新视图状态
        LicenseConverter.updateVenueList(this.state, venues)
        
        // 获取授权状态
        const statusMap = await this.interactor.getAuthStatusMap(venues.map(v => v.id))
        // 更新视图状态
        LicenseConverter.updateAuthStatusMap(this.state, statusMap)
      } finally {
        this.state.loading = false
      }
    },

    // 更新表单数据
    updateFormData: (data: Partial<ILicenseState['formData']>) => {
      Object.assign(this.state.formData, data)
    },

    // 重置表单
    resetForm: () => {
      this.state.formData = LicenseConverter.createEmptyFormData()
    },

    // 生成授权码
    generateAuth: async () => {
      if (!this.computes.currentVenue.value || !this.computes.canGenerate.value) return

      try {
        this.state.loading = true
        // 转换请求参数
        const req = LicenseConverter.toGenerateAuthReq(
          this.computes.currentVenue.value.id,
          this.state.formData
        )
        
        // 调用业务层生成授权码
        const authCode = await this.interactor.generateAuthCode(req)
        if (authCode) {
          // 刷新列表
          await this.actions.refreshList()
          // 重置表单
          this.actions.resetForm()
        }
      } finally {
        this.state.loading = false
      }
    },

    // 强制失效
    forceExpire: async (venueId: string) => {
      try {
        // 二次确认
        await ElMessageBox.confirm(
          '确定要强制失效该门店的授权吗？此操作不可恢复。',
          '强制失效确认',
          {
            type: 'warning',
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }
        )

        this.state.loading = true
        const success = await this.interactor.forceExpire(venueId)
        if (success) {
          // 刷新列表
          await this.actions.refreshList()
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('强制失效失败:', error)
        }
      } finally {
        this.state.loading = false
      }
    },

    // 导出授权列表
    exportAuthList: async () => {
      const venueIds = this.state.venueList.map(v => v.id)
      await this.interactor.exportAuthList(venueIds)
    },

    // 通知即将过期的门店
    notifyExpiringVenues: async () => {
      const expiringVenues = Object.entries(this.state.authStatusMap)
        .filter(([_, status]) => status.status === 1 && status.remainDays <= 15)
        .map(([id]) => id)

      await this.interactor.notifyExpiringVenues(expiringVenues)
    }
  }

  /**
   * 设置生命周期
   */
  private setupLifecycles() {
    // 初始化时加载数据
    this.actions.refreshList()
  }
}

// 导出工厂函数
export function useLicensePresenter(): ILicenseViewModel {
  return new LicensePresenter()
} 