<!-- 授权管理页面 -->
<template>
  <div class="license-generate flex h-full">
    <!-- 左侧列表区域 (60%) -->
    <div class="license-list w-3/5 pr-4 overflow-hidden flex flex-col">
      <!-- 筛选操作栏 -->
      <div class="filter-bar mb-4 bg-white p-4 rounded-lg shadow-sm">
        <el-form :inline="true" class="flex items-center">
          <el-form-item label="状态">
            <el-select
              v-model="vm.state.filter.status"
              multiple
              placeholder="选择状态"
              class="w-48"
            >
              <el-option label="已过期" value="expired" />
              <el-option label="即将到期" value="expiring" />
              <el-option label="有效" value="valid" />
            </el-select>
          </el-form-item>
          <el-form-item label="搜索">
            <el-input
              v-model="vm.state.filter.keyword"
              placeholder="输入门店名称搜索"
              class="w-64"
            >
              <template #suffix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="vm.actions.refreshList">
              刷新
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 列表区域 -->
      <div class="flex-1 overflow-hidden bg-white rounded-lg shadow-sm">
        <el-table
          v-loading="vm.state.loading"
          :data="vm.computes.filteredVenueList.value"
          @row-click="(row: VenueView) => vm.actions.selectVenue(row.id)"
          height="100%"
        >
          <el-table-column label="门店名称" prop="name" min-width="120">
            <template #default="{ row: venue }">
              <div class="flex items-center">
                <span>{{ venue.name }}</span>
                <!-- 状态标识 -->
                <div 
                  v-if="vm.state.authStatusMap[venue.id]"
                  :class="[
                    'ml-2 px-2 py-0.5 rounded text-xs',
                    vm.computes.statusClass.value(vm.state.authStatusMap[venue.id].status)
                  ]"
                >
                  {{ vm.computes.statusText.value(vm.state.authStatusMap[venue.id].status) }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="类型" prop="type" width="100">
            <template #default="{ row: venue }">
              {{ vm.computes.venueTypeText.value(venue.type) }}
            </template>
          </el-table-column>
          <el-table-column label="到期时间" width="160">
            <template #default="{ row: venue }">
              <template v-if="vm.state.authStatusMap[venue.id]">
                {{ vm.state.authStatusMap[venue.id].expireTime }}
                <div class="text-xs text-gray-500">
                  剩余 {{ vm.state.authStatusMap[venue.id].remainDays }} 天
                </div>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row: venue }">
              <el-button 
                link 
                type="primary" 
                @click.stop="vm.actions.selectVenue(venue.id)"
              >
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 右侧操作区域 (40%) -->
    <div class="license-operation w-2/5 pl-4">
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <!-- 未选择门店时的提示 -->
        <div v-if="!vm.computes.currentVenue.value" class="text-center text-gray-500 py-8">
          <el-icon class="text-4xl mb-2"><Select /></el-icon>
          <div>请从左侧选择门店</div>
        </div>

        <!-- 选中门店后的操作表单 -->
        <template v-else>
          <h3 class="text-lg font-medium mb-4 flex items-center justify-between">
            <span>{{ vm.computes.currentVenue.value.name }}</span>
            <el-tag 
              :type="vm.computes.tagType.value(vm.computes.currentAuthStatus.value?.status || 0)" 
              size="small"
            >
              {{ vm.computes.statusText.value(vm.computes.currentAuthStatus.value?.status || 0) }}
            </el-tag>
          </h3>
          
          <el-form 
            label-position="top"
            :model="vm.state.formData"
          >
            <el-form-item 
              label="有效期天数" 
              required
            >
              <el-input-number
                v-model="vm.state.formData.days"
                :min="7"
                :max="365"
                :placeholder="`推荐 ${vm.computes.recommendDays.value} 天`"
                class="w-full"
              />
            </el-form-item>

            <el-form-item label="合同编号">
              <el-input 
                v-model="vm.state.formData.contractNo"
                placeholder="请输入合同编号（选填）"
              />
            </el-form-item>

            <el-form-item label="合同名称">
              <el-input 
                v-model="vm.state.formData.contractName"
                placeholder="请输入合同名称（选填）"
              />
            </el-form-item>

            <el-form-item label="备注信息">
              <el-input 
                v-model="vm.state.formData.remark"
                type="textarea"
                rows="3"
                placeholder="请输入备注信息"
              />
            </el-form-item>

            <div class="flex justify-between mt-6">
              <el-button 
                type="danger" 
                @click="vm.actions.forceExpire(vm.computes.currentVenue.value.id)"
                :disabled="!vm.computes.currentVenue.value"
              >
                强制失效
              </el-button>
              <el-button 
                type="primary" 
                :disabled="!vm.computes.canGenerate.value"
                @click="vm.actions.generateAuth"
              >
                生成授权码
              </el-button>
            </div>
          </el-form>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Search, Select } from '@element-plus/icons-vue'
import type { ILicenseViewModel, VenueView } from './viewmodel'
import { useLicensePresenter } from './presenter'

// 创建视图模型实例
const vm: ILicenseViewModel = useLicensePresenter()
</script>

<style scoped>
.license-generate {
  height: calc(100vh - var(--header-height));
}

/* 呼吸灯效果 */
@keyframes breathing {
  0% { opacity: 0.4; }
  50% { opacity: 1; }
  100% { opacity: 0.4; }
}

.status-expiring {
  animation: breathing 2s ease-in-out infinite;
}
</style> 