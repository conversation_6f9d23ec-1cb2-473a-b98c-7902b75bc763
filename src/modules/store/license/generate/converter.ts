import type { VenueVO } from '@/entities/store/venue'
import type { VenueAuthStatusVO, GenerateAuthReq } from '@/entities/store/license'
import type { VenueView, AuthStatusView, ILicenseState, IFormData } from './viewmodel'

/**
 * 授权管理模块数据转换器
 */
export class LicenseConverter {
  /**
   * 创建初始状态
   */
  static createInitialState(): ILicenseState {
    return {
      venueList: [],
      authStatusMap: {},
      currentVenueId: null,
      loading: false,
      filter: {
        keyword: '',
        status: ['expired', 'expiring'], // 默认显示已过期和即将到期
        sortField: 'priority',
        sortOrder: 'asc'
      },
      formData: {
        days: 30, // 默认30天
        remark: ''
      }
    }
  }

  /**
   * 创建空的表单数据
   */
  static createEmptyFormData(): IFormData {
    return {
      days: 30,
      remark: ''
    }
  }

  /**
   * 将服务端门店实体转换为视图模型
   */
  static toVenueView(entity: VenueVO): VenueView {
    return {
      id: entity.id,
      name: entity.name,
      type: entity.venueType,
      address: entity.address,
      contact: entity.contact,
      phone: entity.contactPhone
    }
  }

  /**
   * 将服务端授权状态实体转换为视图模型
   */
  static toAuthStatusView(entity: VenueAuthStatusVO): AuthStatusView {
    return {
      status: entity.auth_status,
      expireTime: entity.expire_time,
      remainDays: entity.remain_days,
      authType: entity.auth_type,
      contractNo: entity.contract_no,
      contractName: entity.contract_name,
      lastAuthTime: entity.last_auth_time,
      remark: entity.audit_remark
    }
  }

  /**
   * 将表单数据转换为生成授权请求参数
   */
  static toGenerateAuthReq(venueId: string, formData: IFormData): GenerateAuthReq {
    return {
      venue_ids: [venueId],
      status: 2, // 正式授权
      remark: formData.remark,
      templateId: undefined // 可选的审核模板ID
    }
  }

  /**
   * 计算门店优先级（用于排序）
   */
  static getPriority(authStatus: AuthStatusView): number {
    if (authStatus.status === 3) return 1 // 已过期
    if (authStatus.remainDays <= 7) return 2 // 7天内到期
    if (authStatus.remainDays <= 15) return 3 // 15天内到期
    return 4 // 正常
  }

  /**
   * 更新视图状态中的门店列表
   */
  static updateVenueList(state: ILicenseState, entities: VenueVO[]): void {
    state.venueList = entities.map(entity => this.toVenueView(entity))
  }

  /**
   * 更新视图状态中的授权状态映射
   */
  static updateAuthStatusMap(state: ILicenseState, entities: Record<string, VenueAuthStatusVO>): void {
    state.authStatusMap = Object.entries(entities).reduce((map, [id, entity]) => {
      map[id] = this.toAuthStatusView(entity)
      return map
    }, {} as Record<string, AuthStatusView>)
  }

  /**
   * 根据当前状态计算推荐的授权天数
   */
  static getRecommendDays(authStatus: AuthStatusView | null): number {
    if (!authStatus) return 30
    
    // 如果是试用授权，建议90天
    if (authStatus.authType === 1) return 90
    
    // 如果是正式授权，建议365天
    if (authStatus.authType === 2) return 365
    
    // 默认30天
    return 30
  }
} 