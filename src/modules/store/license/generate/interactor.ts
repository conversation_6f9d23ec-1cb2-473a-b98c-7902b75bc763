import { ElMessage } from 'element-plus'
import { getAuthStatus, generateAuth, activateAuth } from '@/api/store/license'
import { queryVenues } from '@/api/store/venue'
import type { VenueAuthStatusVO, GenerateAuthReq } from '@/entities/store/license'
import type { VenueVO, VenuePageQueryParams } from '@/entities/store/venue'

/**
 * 授权管理模块业务交互层
 */
export class LicenseInteractor {
  /**
   * 获取门店列表
   */
  async getVenueList(): Promise<VenueVO[]> {
    try {
      const res = await queryVenues({
        auditStatus: 1, // 只查询已审核通过的门店
        pageNum: 1,     // 第一页
        pageSize: 100   // 默认加载前100条
      } as VenuePageQueryParams)
      return res
    } catch (error) {
      console.error('获取门店列表失败:', error)
      ElMessage.error('获取门店列表失败')
      return []
    }
  }

  /**
   * 获取门店授权状态
   */
  async getAuthStatusMap(venueIds: string[]): Promise<Record<string, VenueAuthStatusVO>> {
    try {
      const statusMap: Record<string, VenueAuthStatusVO> = {}
      await Promise.all(
        venueIds.map(async (id) => {
          const status = await getAuthStatus({ venue_id: id })
          statusMap[id] = status
        })
      )
      return statusMap
    } catch (error) {
      console.error('获取授权状态失败:', error)
      ElMessage.error('获取授权状态失败')
      return {}
    }
  }

  /**
   * 生成授权码
   */
  async generateAuthCode(params: GenerateAuthReq): Promise<string | null> {
    try {
      // 调用生成接口
      const authCode = await generateAuth(params)
      
      // 自动激活授权码
      if (params.venue_ids.length === 1) {
        await this.activateAuthCode(params.venue_ids[0], authCode)
      }
      
      ElMessage.success('授权码生成成功')
      return authCode
    } catch (error) {
      console.error('生成授权码失败:', error)
      ElMessage.error('生成授权码失败')
      return null
    }
  }

  /**
   * 激活授权码
   */
  private async activateAuthCode(venueId: string, authCode: string): Promise<boolean> {
    try {
      await activateAuth({
        venue_id: venueId,
        auth_code: authCode
      })
      return true
    } catch (error) {
      console.error('激活授权码失败:', error)
      ElMessage.warning('授权码已生成，但激活失败')
      return false
    }
  }

  /**
   * 强制失效授权
   */
  async forceExpire(venueId: string): Promise<boolean> {
    try {
      // 调用强制失效接口
      await generateAuth({
        venue_ids: [venueId],
        status: 3, // 强制失效状态
        remark: '管理员手动操作-强制失效'
      })
      
      ElMessage.success('授权已强制失效')
      return true
    } catch (error) {
      console.error('强制失效失败:', error)
      ElMessage.error('强制失效失败')
      return false
    }
  }

  /**
   * 导出授权列表
   */
  async exportAuthList(venueIds: string[]): Promise<void> {
    try {
      // TODO: 实现导出逻辑
      ElMessage.info('导出功能开发中')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    }
  }

  /**
   * 通知即将过期的门店
   */
  async notifyExpiringVenues(venueIds: string[]): Promise<void> {
    try {
      // TODO: 实现通知逻辑
      ElMessage.info('通知功能开发中')
    } catch (error) {
      console.error('发送通知失败:', error)
      ElMessage.error('发送通知失败')
    }
  }
}

// 导出工厂函数
export function useLicenseInteractor(): LicenseInteractor {
  return new LicenseInteractor()
} 