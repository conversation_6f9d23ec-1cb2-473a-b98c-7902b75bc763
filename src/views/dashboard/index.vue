```vue
<template>
  <div class="page-container">
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <el-card shadow="hover" :body-style="{ padding: '20px' }">
        <div class="flex items-center">
          <el-icon class="text-3xl text-primary mr-4"><Shop /></el-icon>
          <div>
            <div class="text-gray-500">总门店数</div>
            <div class="text-2xl font-bold">{{ stats.totalStores }}</div>
          </div>
        </div>
      </el-card>
      
      <el-card shadow="hover" :body-style="{ padding: '20px' }">
        <div class="flex items-center">
          <el-icon class="text-3xl text-success mr-4"><CircleCheck /></el-icon>
          <div>
            <div class="text-gray-500">已审核门店</div>
            <div class="text-2xl font-bold">{{ stats.approvedStores }}</div>
          </div>
        </div>
      </el-card>
      
      <el-card shadow="hover" :body-style="{ padding: '20px' }">
        <div class="flex items-center">
          <el-icon class="text-3xl text-warning mr-4"><Timer /></el-icon>
          <div>
            <div class="text-gray-500">待审核申请</div>
            <div class="text-2xl font-bold">{{ stats.pendingApplications }}</div>
          </div>
        </div>
      </el-card>
      
      <el-card shadow="hover" :body-style="{ padding: '20px' }">
        <div class="flex items-center">
          <el-icon class="text-3xl text-danger mr-4"><Warning /></el-icon>
          <div>
            <div class="text-gray-500">异常门店</div>
            <div class="text-2xl font-bold">{{ stats.abnormalStores }}</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 最近申请列表 -->
    <el-card class="mb-6">
      <template #header>
        <div class="flex items-center justify-between">
          <span>最近申请</span>
          <el-button type="primary" text>查看全部</el-button>
        </div>
      </template>
      
      <el-table :data="recentApplications" style="width: 100%">
        <el-table-column prop="storeName" label="门店名称" />
        <el-table-column prop="applicant" label="申请人" />
        <el-table-column prop="type" label="申请类型" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="row.status === '待审核' ? 'warning' : 'success'">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="申请时间" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Shop, CircleCheck, Timer, Warning } from '@element-plus/icons-vue'

// 模拟数据
const stats = ref({
  totalStores: 156,
  approvedStores: 142,
  pendingApplications: 8,
  abnormalStores: 3
})

const recentApplications = ref([
  {
    storeName: '北京朝阳店',
    applicant: '张三',
    type: '开店申请',
    status: '待审核',
    createTime: '2024-03-20 10:30:00'
  },
  {
    storeName: '上海浦东店',
    applicant: '李四',
    type: '信息变更',
    status: '已通过',
    createTime: '2024-03-19 15:20:00'
  }
  // 更多数据...
])
</script>
```