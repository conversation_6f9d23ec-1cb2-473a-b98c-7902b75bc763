# 数据导入插件化架构说明

## 概述

数据导入模块采用插件化架构设计，将不同类型的数据导入功能拆分为独立的插件，每个插件负责特定类型数据的导入逻辑。这种设计提供了良好的扩展性、可维护性和模块化。

## 架构设计

### 核心组件

#### 1. 插件接口定义 (`core/types.ts`)
- **DataImportPlugin**: 插件主接口，定义插件的基本信息和功能组件
- **TemplateGenerator**: 模板生成器接口，负责生成导入模板
- **DataValidator**: 数据验证器接口，负责数据验证逻辑
- **DataPreviewer**: 数据预览器接口，负责数据预览和格式化
- **DataImporter**: 数据导入器接口，负责实际的数据导入操作

#### 2. 基础类 (`core/BasePlugin.ts`)
- **BaseDataImportPlugin**: 插件抽象基类，提供默认实现
- **BaseTemplateGenerator**: 模板生成器基类
- **BaseDataValidator**: 数据验证器基类
- **BaseDataPreviewer**: 数据预览器基类
- **BaseDataImporter**: 数据导入器基类

#### 3. 插件管理器 (`core/PluginManager.ts`)
- **DataImportPluginManager**: 插件管理器，负责插件的注册、启用、禁用和生命周期管理
- 提供插件状态管理和错误处理机制

#### 4. 插件注册系统 (`core/PluginRegistry.ts`)
- **PluginRegistry**: 插件注册表，自动发现和注册所有插件
- 提供插件查询和管理功能

### 插件目录结构

```
src/modules/store/data-import/
├── core/                           # 核心框架
│   ├── types.ts                   # 类型定义
│   ├── BasePlugin.ts              # 基础类
│   ├── PluginManager.ts           # 插件管理器
│   └── PluginRegistry.ts          # 插件注册系统
├── plugins/                       # 插件目录
│   ├── rooms/                     # 包厢导入插件
│   │   ├── index.ts              # 插件主入口
│   │   ├── TemplateGenerator.ts  # 模板生成器
│   │   ├── DataValidator.ts      # 数据验证器
│   │   ├── DataPreviewer.ts      # 数据预览器
│   │   └── DataImporter.ts       # 数据导入器
│   ├── products/                  # 商品导入插件
│   ├── members/                   # 会员导入插件
│   ├── inventory/                 # 库存导入插件
│   └── wine-storage/              # 存酒导入插件
├── components/                    # 共享组件
├── views/                         # 页面视图
└── index.ts                       # 模块主入口
```

## 插件实现

### 插件基本结构

每个插件包含以下组件：

1. **插件主类**: 继承 `BaseDataImportPlugin`，定义插件基本信息
2. **模板生成器**: 负责生成CSV导入模板和验证规则
3. **数据验证器**: 负责数据格式验证和错误检查
4. **数据预览器**: 负责数据预览和格式化显示
5. **数据导入器**: 负责实际的数据导入操作

### 插件生命周期

1. **安装** (`onInstall`): 插件首次注册时执行
2. **启用** (`onEnable`): 插件启用时执行
3. **禁用** (`onDisable`): 插件禁用时执行
4. **卸载** (`onUninstall`): 插件卸载时执行
5. **导入前** (`beforeImport`): 数据导入前执行
6. **导入后** (`afterImport`): 数据导入后执行

## 当前插件状态

### 已实现插件

#### 1. 包厢导入插件 (`rooms`)
- **状态**: ✅ 完整实现
- **功能**: 包厢基础信息、类型、容量等数据导入
- **字段**: 包厢编号、名称、类型、容量、面积、时价、描述

#### 2. 商品导入插件 (`products`)
- **状态**: 🚧 占位符实现
- **功能**: 商品信息、价格、分类等数据导入
- **待开发**: 具体业务逻辑和字段定义

#### 3. 会员导入插件 (`members`)
- **状态**: 🚧 占位符实现
- **功能**: 会员基础信息、等级、联系方式等数据导入
- **待开发**: 具体业务逻辑和字段定义

#### 4. 库存导入插件 (`inventory`)
- **状态**: 🚧 占位符实现
- **功能**: 商品库存数量、安全库存等数据导入
- **待开发**: 具体业务逻辑和字段定义

#### 5. 存酒导入插件 (`wine-storage`)
- **状态**: 🚧 占位符实现
- **功能**: 会员存酒记录、酒品信息等数据导入
- **待开发**: 具体业务逻辑和字段定义

## 技术特性

### 1. 类型安全
- 使用 TypeScript 确保类型安全
- 完整的接口定义和类型约束
- 编译时错误检查

### 2. 扩展性
- 插件化架构，易于添加新的导入类型
- 标准化的插件接口，降低开发成本
- 热插拔支持，无需重启系统

### 3. 可维护性
- 职责分离，每个插件独立维护
- 统一的错误处理和日志记录
- 清晰的代码结构和文档

### 4. 用户体验
- 统一的用户界面和交互流程
- 实时的导入进度和状态反馈
- 详细的错误信息和修复建议

## 开发指南

### 创建新插件

1. 在 `plugins/` 目录下创建插件文件夹
2. 实现插件主类，继承 `BaseDataImportPlugin`
3. 实现四个核心组件：TemplateGenerator、DataValidator、DataPreviewer、DataImporter
4. 在 `PluginRegistry.ts` 中注册新插件

### 插件开发最佳实践

1. **遵循接口规范**: 严格按照接口定义实现功能
2. **错误处理**: 提供详细的错误信息和处理建议
3. **数据验证**: 实现完整的数据验证逻辑
4. **用户体验**: 提供清晰的操作指引和反馈
5. **性能优化**: 处理大量数据时考虑性能影响

## 部署和配置

### 插件管理
- 插件自动注册，无需手动配置
- 支持插件的启用/禁用切换
- 插件状态持久化存储

### 性能考虑
- 大文件导入时的内存管理
- 分批处理和进度反馈
- 错误恢复和重试机制

## 未来规划

1. **插件市场**: 支持第三方插件开发和分发
2. **可视化配置**: 提供插件配置界面
3. **数据映射**: 支持字段映射和数据转换
4. **导入历史**: 完整的导入历史记录和回滚功能
5. **API集成**: 支持从外部系统直接导入数据

## 版本信息

- **当前版本**: 1.0.0
- **最后更新**: 2024年12月
- **维护状态**: 积极开发中

---

*此文档随架构演进持续更新* 